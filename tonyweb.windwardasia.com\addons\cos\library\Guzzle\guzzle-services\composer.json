{"name": "guzzlehttp/guzzle-services", "description": "Provides an implementation of the Guzzle Command library that uses Guzzle service descriptions to describe web services, serialize requests, and parse responses into easy to use model structures.", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Konafets"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "require": {"php": "^7.2.5 || ^8.0", "guzzlehttp/guzzle": "^7.4.1", "guzzlehttp/command": "^1.2.2", "guzzlehttp/psr7": "^1.8.3 || ^2.1", "guzzlehttp/uri-template": "^1.0.1"}, "require-dev": {"phpunit/phpunit": "^8.5.19 || ^9.5.8"}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\Guzzle\\": "src/"}}, "autoload-dev": {"psr-4": {"GuzzleHttp\\Tests\\Command\\Guzzle\\": "tests/"}}, "suggest": {"gimler/guzzle-description-loader": "^0.0.4"}, "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "config": {"preferred-install": "dist", "sort-packages": true}}