<?php

namespace app\partner\controller\user;

use app\common\controller\PartnerBase;
use app\common\library\Auth;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use app\common\library\Redis;
use think\Env;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;


/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class UserSon extends PartnerBase
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('User');
        $this->view->assign("IsPartnerList", $this->model->getIsPartnerList());
        $this->view->assign("partnerStatusList", $this->model->getPartnerStatusList());
    }

    /**
     * 查看
     */
    public function index()
    {   $p_user = $this->request->param('p_user');
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->where(function($sql)use($p_user){
                    if($p_user){
                        $sql->where('p_user',$p_user);
                    }
                    return $sql;
                })
                ->where($where)
                ->order($sort, $order)
                ->field('id,nickname,mobile,p_user,partner_id,is_partner,partner_status')
                ->paginate($limit);
            foreach ($list as $k => &$v) {
                $v->partner_name = '';
                $v->partner_rate = '';
                if($v->is_partner){
                    
                    $v->is_partner_attr = '推广人';
                    if($v->partner_id){
                        $i = Db::name('partner_level')->where('id',$v->partner_id)->find();
                        $v->partner_name = $i['name'];
                        $v->partner_rate = $i['rate'];
                    }
                    
                }else{
                    $v->is_partner_attr = '普通';
                }
            }//dump( collection($list)->toArray());
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        $this->view->assign('p_user', $p_user);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        return parent::add();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
        // return parent::edit($ids);
        return $this->treu_edit($ids);
    }


    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function treu_edit($ids = null)
    {
        $row = $this->model->get($ids);
        $yuan_status = $row->status;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            $this->view->assign('row_json', json_encode($row));
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }



    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }
    
    
    public function index_all(){
        $custom = (array)$this->request->request("custom/a");
        $user_id = $custom['user_id'];
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {//
                // $arr = $this->selectpage()->getContent();
                // $arr = json_decode($arr,true);
                $nickname = Db::name('user')->where('id',$user_id)->value('nickname');
                $list = [['id'=>0,'nickname'=>'所有下级'],['id'=>$user_id,'nickname'=>$nickname]];
                $this->select_user([['id'=>$user_id,'nickname'=>$nickname]],$list);
                $array['list'] = $list;
                $array['total'] = count($array['list']);
                return json($array);
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with('group')
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
            }
            $result = array("total" => ($list->total())+1, "rows" => ['id'=>0,'nickname'=>'所有人']+$list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
    
    public function select_user($li,&$arr){
        foreach($li as $y){
            $f = Db::name('user')->where('p_user',$y['id'])->field('id,nickname')->select();
            if(!empty($f)){
                $arr = array_merge($arr,$f);
                $this->select_user($f,$arr);
            }else{
                return $arr;
            }
        }
        
        
    }
    
    
    
}