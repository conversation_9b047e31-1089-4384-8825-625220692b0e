<?php

namespace app\admin\controller\addons\alioss;

use app\common\model\Attachment;
use OSS\OssClient;
use think\Controller;

class Index extends Controller
{
    /**
     * 获取签名
     */
    public function params()
    {
        $this->check();
        $name = $this->request->post('name');
        $md5 = $this->request->post('md5');
        $chunk = $this->request->post('chunk');

        $auth = new \addons\alioss\library\Auth();
        $params = $auth->params($name, $md5);
        $params['OSSAccessKeyId'] = $params['id'];
        $params['success_action_status'] = 200;
        $config = get_addon_config('alioss');

        if ($chunk) {
            $oss = new OssClient($config['accessKeyId'], $config['accessKeySecret'], $config['endpoint']);
            // 初始化
            $fileSize = $this->request->post('size');
            $chunkSize = $this->request->post('chunksize');
            $uploadId = $oss->initiateMultipartUpload($config['bucket'], $params['key']);
            $params['uploadId'] = $uploadId;
            $params['parts'] = $oss->generateMultiuploadParts($fileSize, $chunkSize);
            $params['partsAuthorization'] = [];
            $date = gmdate('D, d M Y H:i:s \G\M\T');
            foreach ($params['parts'] as $index => $part) {
                $partNumber = $index + 1;
                $signstr = "PUT\n\n\n{$date}\nx-oss-date:{$date}\n/{$config['bucket']}/{$params['key']}?partNumber={$partNumber}&uploadId={$uploadId}";
                $authorization = base64_encode(hash_hmac('sha1', $signstr, $config['accessKeySecret'], true));
                $params['partsAuthorization'][$index] = $authorization;
            }
            $params['date'] = $date;
        }

        $this->success('', null, $params);
        return;
    }

    /**
     * 回调
     */
    public function notify()
    {
        $this->check();
        $size = $this->request->post('size/d');
        $name = $this->request->post('name', '');
        $md5 = $this->request->post('md5', '');
        $type = $this->request->post('type', '');
        $url = $this->request->post('url', '');
        $width = $this->request->post('width/d');
        $height = $this->request->post('height/d');
        $category = $this->request->post('category', '');
        $category = array_key_exists($category, config('site.attachmentcategory') ?? []) ? $category : '';
        $suffix = strtolower(pathinfo($name, PATHINFO_EXTENSION));
        $suffix = $suffix && preg_match("/^[a-zA-Z0-9]+$/", $suffix) ? $suffix : 'file';
        $attachment = Attachment::where('url', $url)->where('storage', 'alioss')->find();
        if (!$attachment) {
            $params = array(
                'category'    => $category,
                'admin_id'    => (int)session('admin.id'),
                'user_id'     => (int)cookie('uid'),
                'filesize'    => $size,
                'filename'    => $name,
                'imagewidth'  => $width,
                'imageheight' => $height,
                'imagetype'   => $suffix,
                'imageframes' => 0,
                'mimetype'    => $type,
                'url'         => $url,
                'uploadtime'  => time(),
                'storage'     => 'alioss',
                'sha1'        => $md5,
            );
            Attachment::create($params, true);
        }
        $this->success();
        return;
    }


    /**
     * 检查签名是否正确或过期
     */
    protected function check()
    {
        $aliosstoken = $this->request->post('aliosstoken', '', 'trim');
        if (!$aliosstoken) {
            $this->error("参数不正确");
        }
        $config = get_addon_config('alioss');
        list($accessKeyId, $sign, $data) = explode(':', $aliosstoken);
        if (!$accessKeyId || !$sign || !$data) {
            $this->error("参数不能为空");
        }
        if ($accessKeyId !== $config['accessKeyId']) {
            $this->error("参数不正确");
        }
        if ($sign !== base64_encode(hash_hmac('sha1', base64_decode($data), $config['accessKeySecret'], true))) {
            $this->error("签名不正确");
        }
        $json = json_decode(base64_decode($data), true);
        if ($json['deadline'] < time()) {
            $this->error("请求已经超时");
        }
    }
}