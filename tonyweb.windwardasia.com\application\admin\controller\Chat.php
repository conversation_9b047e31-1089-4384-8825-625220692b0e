<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Request;

/**
 * 拉黑管理
 *
 * @icon fa fa-circle-o
 */
class Chat extends Backend
{

    /**
     * ForbidUser模型对象
     * @var \app\admin\model\Chat
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Chat();
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->model = new \app\admin\model\Chat();

    }


    /**
     * 查看
     */
    public function index()
    {
        $req     = Request::instance();
        $from_id = $req->param('from_id', '');
        $read    = $req->param('read', 'all');
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list   = $this->model
                ->where(function ($sql) use ($from_id, $read) {
                    if ($from_id) {
                        $sql->where('from_id', $from_id);
                        /*$sql->whereor('to_id',$from_id);*/
                    }
                    if ($read != 'all') {
                        $sql->where('read', $read);
                    }
                    return $sql;
                })
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit)->each(function ($item) {
                    $item['nickname'] = \app\admin\model\User::where('id', $item->to_id)->value('nickname');
                    return $item;
                });
            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $row->nickname = \app\admin\model\User::where('id', $row->to_id)->value('nickname');
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name     = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

}
