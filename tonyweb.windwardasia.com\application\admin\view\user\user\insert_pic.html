<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id}">
    

    
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">相册图：</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-detail_images" class="form-control" size="50" name="row[detail_images]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-detail_images" class="btn btn-danger faupload" data-input-id="c-detail_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-detail_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-detail_images" class="btn btn-primary fachoose" data-input-id="c-detail_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-detail_images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-detail_images"></ul>
        </div>
    </div>
    
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>












    