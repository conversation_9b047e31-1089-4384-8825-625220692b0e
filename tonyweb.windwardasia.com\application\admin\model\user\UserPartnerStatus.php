<?php

namespace app\admin\model\user;

use think\Model;


class UserPartnerStatus extends Model
{

    

    

    // 表名
    protected $name = 'user_partner_status';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id')->setEagerlyType(0);
    }
    public function users()
    {
        return $this->belongsTo('app\admin\model\User', 'partner_id', 'id')->setEagerlyType(0);
    }
    public function level()
    {
        return $this->belongsTo('app\admin\model\PartnerLevel', 'old_partner_level', 'id',[],'LEFT')->setEagerlyType(0);
    }

    public function levels()
    {
        return $this->belongsTo('app\admin\model\PartnerLevel', 'new_partner_level', 'id',[],'LEFT')->setEagerlyType(0);
    }
}
