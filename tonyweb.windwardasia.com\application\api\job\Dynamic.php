<?php

namespace app\api\job;

use app\admin\model\DynamicImg;
use app\admin\model\financial\Order;
use app\admin\model\User;
use app\common\library\Redis;
use think\Db;
use think\Env;
use think\queue\Job;

class Dynamic
{

    public function fire(Job $job,$data)
    {
        if ($data && isset($data['data'])){
            $datas = $data['data'];
            $user = User::where('forever_on','1')->orderRaw('rand()')->limit(1)->find();
            if ($user){
                $user_id = $user['id'];
                $content = $datas['content'];
                $location = $datas['location'];
                $imgs = $datas['imgs'];
                $lng = $datas['lng'];
                $lat = $datas['lat'];
                $createtime = $datas['createtime'];
                $source_type = '0';
                if($imgs!=''){
                    $img_list = explode(',', $imgs);
                    $source_type = '1';
                }

                $add = [
                    'user_id'=>$user_id,
                    'content'=>$content,
                    'have_source'=>$source_type,
                    'location'=>$location,
                    'lat'=>$lat,
                    'lng'=>$lng,
                    'status'  =>'1',
                    'createtime'=>$createtime
                ];

                $dynamic_id = (new \app\admin\model\Dynamic)->insertGetId($add);
                Db::name('user_base')->where('user_id',$user_id)->setInc('dynamic_num',1);
                //动态图
                if($imgs!=''){
                    foreach($img_list as $v){
                        (new \app\admin\model\DynamicImg)->save([
                            'dynamic_id'=>$dynamic_id,
                            'user_id'=>$user_id,
                            'image'=>"/".$v,
                            'source_type'=>$source_type
                        ]);
                    }
                }
                $prefix = Env::get('redis.redis_prefix');
                $redis = (new Redis())->handler;
                $config = $redis->hmget($prefix.'global_config',['global_dynamic_great','global_dynamic_cmt']);
                if($config['global_dynamic_great']){
                    $redis->rpush($prefix.'crontab_dynamic_great',strval($dynamic_id));
                    $redis->setex($prefix.'dynamic_greating_'.$dynamic_id,86400,'1');
                }
                if($config['global_dynamic_cmt']){
                    $redis->rpush($prefix.'crontab_dynamic_cmt',strval($dynamic_id));
                    $redis->setex($prefix.'dynamic_cmting_'.$dynamic_id,86400,'1');
                }
            }else{
                $job->delete();
            }
            $job->delete();
            return true;
        }
    }
}