<?php

namespace app\partner\controller;

use app\admin\model\User;
use app\common\controller\PartnerBase;
use think\Db;

/**
 * 会员余额变动管理
 *
 * @icon fa fa-circle-o
 */
class MoneyLog extends PartnerBase
{

    /**
     * MoneyLog模型对象
     * @var \app\admin\model\MoneyLog
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\partner\model\MoneyLog;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $user_id = $this->request->param('user_id');
            $list = $this->model
                ->with(['user'])
                ->where(function($sql)use($user_id){
                    if($user_id){$sql->where('user_id',$user_id);}else{
                        $sql->where('user_id',$this->auth->id);
                    }
                })
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
            $add_money = 0;
            $dec_price = 0;
            foreach ($list as $row) {
                if ($row->money > 0){
                    $add_money += $row->money;
                }
                if ($row->money < 0){
                    $dec_price += $row->money;
                }

                $row->visible(['id','money','before','after','memo','createtime']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['nickname']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items(),'add_money'=>sprintf('%.2f',bcadd($add_money,$dec_price,2)),'dec_price'=>sprintf('%.2f',$dec_price));

            return json($result);
        }
        return $this->view->fetch();
    }



    public function get_inc_money(){
        // $data = $this->request->param();return json($data);
        // $this->success('success',json_encode($data),'');
        $pid = $this->auth->id;
        $new_id = $this->request->param('id');
        $time_start = $this->request->param('time_start/s','');
        $time_end = $this->request->param('time_end/s','');
        if($time_start=='' || $time_end==''){
            return json(['code'=>0,'data'=>[]]);
        }
        $time_start_int = strtotime($time_start." 00:00:00");
        $time_end_int = strtotime($time_end." 23:59:59");
        if($time_start_int===false || $time_end_int===false){
            return json(['code'=>0,'data'=>[]]);
        }
        if ($new_id){
            $p_id = [$new_id];
            $ids = User::where(['p_user'=>$new_id])->column('id');
            $tow_ids = User::where('p_user','in',$ids)->column('id');
            $id = $ids + $p_id + $tow_ids;
//个人增加概况数据（总邀请用户、新增邀请用户、总消费金额、周期内消费金额 总佣金 周期内总佣金）可按时间查询
            //推广人后台增加概况数据（总邀请用户、新增邀请用户、总消费金额、周期内消费金额 总佣金 周期内总佣金）可按时间查询
            //总邀请人数
            $total_invite_num = User::where('p_user',$new_id)->whereBetween('createtime',[$time_start_int,$time_end_int])->count();
            //今日新增
            $today_invite_num = User::where('p_user',$new_id)->whereBetween('createtime',[$time_start_int,$time_end_int])->count();

            //总消费金额
            $zs_total = \app\admin\model\RechargeOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('payamount');
            $vip_total = \app\admin\model\VipOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('price');
            $total_diff_money = bcadd($zs_total,$vip_total,2);
            $today_zs_total = \app\admin\model\RechargeOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('payamount');
            $today_vip_total = \app\admin\model\VipOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('price');
            $today_diff_money = bcadd($today_zs_total,$today_vip_total,2);

            //总佣金
            $total_income = \app\admin\model\MoneyLog::where('user_id','in',$id)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('money');
            $totday_income = \app\admin\model\MoneyLog::where('user_id','in',$id)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('money');
            $data = ['code'=>1,'data'=>[
                $total_invite_num,
                $today_invite_num,
                $total_diff_money,
                $today_diff_money,
                $total_income,
                $totday_income
            ]];
        }
        else{
            $id = [];
            $ids = User::where(['p_user'=>$pid])->column('id');
            $tow_ids = User::where('p_user','in',$ids)->column('id');
            $pids = [$pid];
            $id  = $ids + $pids + $tow_ids;
            //个人增加概况数据（总邀请用户、新增邀请用户、总消费金额、周期内消费金额 总佣金 周期内总佣金）可按时间查询
            //推广人后台增加概况数据（总邀请用户、新增邀请用户、总消费金额、周期内消费金额 总佣金 周期内总佣金）可按时间查询
            //总邀请人数
            $total_invite_num = User::where('p_user',$pid)->whereBetween('createtime',[$time_start_int,$time_end_int])->count();
            //今日新增
            $today_invite_num = User::where('p_user',$pid)->whereBetween('createtime',[$time_start_int,$time_end_int])->count();

            //总消费金额
            $zs_total = \app\admin\model\RechargeOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('payamount');
            $vip_total = \app\admin\model\VipOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('price');
            $total_diff_money = bcadd($zs_total,$vip_total,2);
            $today_zs_total = \app\admin\model\RechargeOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('payamount');
            $today_vip_total = \app\admin\model\VipOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('price');
            $today_diff_money = bcadd($today_zs_total,$today_vip_total,2);

            //总佣金
            $total_income = \app\admin\model\MoneyLog::where('user_id','in',$id)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('money');
            $totday_income = \app\admin\model\MoneyLog::where('user_id','in',$id)->whereBetween('createtime',[$time_start_int,$time_end_int])->sum('money');
            $data = ['code'=>1,'data'=>[
                $total_invite_num,
                $today_invite_num,
                $total_diff_money,
                $today_diff_money,
                $total_income,
                $totday_income
            ]];
        }

        return json($data);
    }

    public function get_inc_default(){
        $pid = $this->auth->id;
        $id = $this->request->param('id');
        $day_begin = strtotime(date('Y-m-d'));
        $day_end = strtotime(date('Y-m-d',strtotime('+1 day')));

        //近七天
        $week_begin = strtotime(date("Y-m-d",strtotime("-7 day")));
        $week_end = time();


        //近三十天
        $month_begin = strtotime(date("Y-m-d",strtotime("-30 day")));
        $month_end = time();


        //近九十天
        $month_beginLast = strtotime(date("Y-m-d",strtotime("-90 day")));
        $month_endLast = time();

        //今日
        $user_inc_day = Db::name('user')->where('p_user',$pid)->whereBetween('createtime',[$day_begin,$day_end])->count('id');
        if(!$user_inc_day){$user_inc_day = 0;}

        $partner_inc_day = Db::name('user')->where('p_user',$pid)->whereBetween('is_partner_time',[$day_begin,$day_end])->count('id');
        if(!$partner_inc_day){$partner_inc_day = 0;}

        $recharge_sum_day = Db::name('recharge_order')->where('user_id',$pid)->whereBetween('paytime',[$day_begin,$day_end])->where('status','1')->sum('payamount');
        if(!$recharge_sum_day){$recharge_sum_day = 0;}

        $dec_money_day = Db::name('user_money_log')->where('user_id',$pid)->whereBetween('createtime',[$day_begin,$day_end])->whereIn('status',['2','3'])->sum('money');
        if(!$dec_money_day){$dec_money_day = 0;}
        $dy_day =   bcsub($recharge_sum_day,$dec_money_day,2);

        //近七天
        $user_inc_week = Db::name('user')->where('p_user',$pid)->whereBetween('createtime',[$week_begin,$week_end])->count('id');
        if(!$user_inc_week){$user_inc_week = 0;}

        $partner_inc_week = Db::name('user')->where('p_user',$pid)->whereBetween('is_partner_time',[$week_begin,$week_end])->count('id');
        if(!$partner_inc_week){$partner_inc_week = 0;}

        $recharge_sum_week = Db::name('recharge_order')->where('user_id',$pid)->whereBetween('createtime',[$week_begin,$week_end])->where('status','1')->sum('payamount');
        if(!$recharge_sum_week){$recharge_sum_week = 0;}

        $dec_money_week = Db::name('user_money_log')->where('user_id',$pid)->whereBetween('createtime',[$week_begin,$week_end])->whereIn('status',['2','3'])->sum('money');
        if(!$dec_money_week){$dec_money_week = 0;}
        $dy_week =   bcsub($recharge_sum_week,$dec_money_week,2);

        //近三十天
        $user_inc_month = Db::name('user')->where('p_user',$pid)->whereBetween('createtime',[$month_begin,$month_end])->count('id');
        if(!$user_inc_month){$user_inc_month = 0;}

        $partner_inc_month = Db::name('user')->where('p_user',$pid)->whereBetween('is_partner_time',[$month_begin,$month_end])->count('id');
        if(!$partner_inc_month){$partner_inc_month = 0;}

        $recharge_sum_month = Db::name('recharge_order')->where('user_id',$pid)->whereBetween('createtime',[$month_begin,$month_end])->where('status','1')->sum('payamount');
        if(!$recharge_sum_month){$recharge_sum_month = 0;}

        $dec_money_month = Db::name('user_money_log')->where('user_id',$pid)->whereBetween('createtime',[$month_begin,$month_end])->whereIn('status',['2','3'])->sum('money');
        if(!$dec_money_month){$dec_money_month = 0;}
        $dy_month =   bcsub($recharge_sum_month,$dec_money_month,2);

        //近90天
        $user_inc_lastmonth = Db::name('user')->where('p_user',$pid)->whereBetween('createtime',[$month_beginLast,$month_endLast])->count('id');
        if(!$user_inc_lastmonth){$user_inc_lastmonth = 0;}

        $partner_inc_lastmonth = Db::name('user')->where('p_user',$pid)->whereBetween('is_partner_time',[$month_beginLast,$month_endLast])->count('id');
        if(!$partner_inc_lastmonth){$partner_inc_lastmonth = 0;}

        $recharge_sum_lastmonth = Db::name('recharge_order')->where('user_id',$pid)->whereBetween('createtime',[$month_beginLast,$month_endLast])->where('status','1')->sum('payamount');
        if(!$recharge_sum_lastmonth){$recharge_sum_lastmonth = 0;}

        $dec_money_lastmonth = Db::name('user_money_log')->where('user_id',$pid)->whereBetween('createtime',[$month_beginLast,$month_endLast])->whereIn('status',['2','3'])->sum('money');
        if(!$dec_money_lastmonth){$dec_money_lastmonth = 0;}
        $dy_lastmonth =   bcsub($recharge_sum_lastmonth,$dec_money_lastmonth,2);
        $data = [
            'code'=>1,
            'data'=>[
                'day'=>[
                    $user_inc_day,
                    $partner_inc_day,
                    $recharge_sum_day,
                    $dec_money_day,
                    $dy_day,
                ],

                'week'=>[
                    $user_inc_week,
                    $partner_inc_week,
                    $recharge_sum_week,
                    $dec_money_week,
                    $dy_week,
                ],

                'month'=>[
                    $user_inc_month,
                    $partner_inc_month,
                    $recharge_sum_month,
                    $dec_money_month,
                    $dy_month,
                ],
                'lastmonth'=>[
                    $user_inc_lastmonth,
                    $partner_inc_lastmonth,
                    $recharge_sum_lastmonth,
                    $dec_money_lastmonth,
                    $dy_lastmonth,
                ]
            ]
        ];
        return json($data);
    }
    public function get_inc_defaults(){
        $pid = $this->auth->id;
        $day_begin = strtotime(date('Y-m-d'));
        $day_end = strtotime(date('Y-m-d',strtotime('+1 day')));
        $new_id = $this->request->param('id');
        if ($new_id){
            $p_id = [$new_id];
            $ids = User::where(['p_user'=>$new_id])->column('id');
            $tow_ids = User::where('p_user','in',$ids)->column('id');
            $id = $ids + $p_id + $tow_ids;
//个人增加概况数据（总邀请用户、新增邀请用户、总消费金额、周期内消费金额 总佣金 周期内总佣金）可按时间查询
            //推广人后台增加概况数据（总邀请用户、新增邀请用户、总消费金额、周期内消费金额 总佣金 周期内总佣金）可按时间查询
            //总邀请人数
            $total_invite_num = User::where('p_user','in',$new_id)->count();
            //今日新增
            $today_invite_num = User::where('p_user','in',$new_id)->whereBetween('createtime',[$day_begin,$day_end])->count();
            //总消费金额
            $zs_total = \app\admin\model\RechargeOrder::where('user_id','in',$id)->where('status',1)->sum('payamount');
            $vip_total = \app\admin\model\VipOrder::where('user_id','in',$id)->where('status',1)->sum('price');
            $total_diff_money = bcadd($zs_total,$vip_total,2);
            $today_zs_total = \app\admin\model\RechargeOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$day_begin,$day_end])->sum('payamount');
            $today_vip_total = \app\admin\model\VipOrder::where('user_id','in',$id)->where('status',1)->whereBetween('createtime',[$day_begin,$day_end])->sum('price');
            $today_diff_money = bcadd($today_zs_total,$today_vip_total,2);

            //总佣金
            $total_income = \app\admin\model\MoneyLog::where('user_id','in',$id)->sum('money');
            $totday_income = \app\admin\model\MoneyLog::where('user_id','in',$id)->whereBetween('createtime',[$day_begin,$day_end])->sum('money');
            $data = [
                'code'  =>  1,
                'data'  =>  [
                    'day'=>[
                        $total_invite_num,
                        $today_invite_num,
                        $total_diff_money,
                        $today_diff_money,
                        $total_income,
                        $totday_income,
                    ]
                ]
            ];

        }
        else {
            $p_id = [$pid];
            $ids = User::where(['p_user' => $pid])->column('id');
            $tow_ids = User::where('p_user','in',$ids)->column('id');
            $id = $ids + $p_id + $tow_ids;
            //个人增加概况数据（总邀请用户、新增邀请用户、总消费金额、周期内消费金额 总佣金 周期内总佣金）可按时间查询
            //推广人后台增加概况数据（总邀请用户、新增邀请用户、总消费金额、周期内消费金额 总佣金 周期内总佣金）可按时间查询
            //总邀请人数
            $total_invite_num = User::where('p_user', 'in', $pid)->count();
            //今日新增
            $today_invite_num = User::where('p_user', 'in', $pid)->whereBetween('createtime', [$day_begin, $day_end])->count();


            //总消费金额

            $zs_total = \app\admin\model\RechargeOrder::where('user_id', 'in', $id)->where('status', 1)->sum('payamount');
            $vip_total = \app\admin\model\VipOrder::where('user_id', 'in', $id)->where('status', 1)->sum('price');
            $total_diff_money = bcadd($zs_total, $vip_total, 2);
            $today_zs_total = \app\admin\model\RechargeOrder::where('user_id', 'in', $id)->where('status', 1)->whereBetween('createtime', [$day_begin, $day_end])->sum('payamount');
            $today_vip_total = \app\admin\model\VipOrder::where('user_id', 'in', $id)->where('status', 1)->whereBetween('createtime', [$day_begin, $day_end])->sum('price');
            $today_diff_money = bcadd($today_zs_total, $today_vip_total, 2);

            //总佣金
            $total_income = \app\admin\model\MoneyLog::where('user_id', 'in',$id)->sum('money');
            $totday_income = \app\admin\model\MoneyLog::where('user_id', 'in',$id)->whereBetween('createtime', [$day_begin, $day_end])->sum('money');
//        var_dump(Db::name('user_money_log')->getLastSql());
            $data = [
                'code' => 1,
                'data' => [
                    'day' => [
                        $total_invite_num,
                        $today_invite_num,
                        $total_diff_money,
                        $today_diff_money,
                        $total_income,
                        $totday_income,
                    ]
                ]
            ];

        }
        return json($data);
    }


}
