<?php

namespace app\admin\model;

use think\Model;


class Gift extends Model
{

    

    

    // 表名
    protected $name = 'gift';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'on_switch_text'
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
        });
    }

    
    public function getOnSwitchList()
    {
        return ['1' => __('On_switch 1'), '0' => __('On_switch 0')];
    }


    public function getOnSwitchTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['on_switch']) ? $data['on_switch'] : '');
        $list = $this->getOnSwitchList();
        return isset($list[$value]) ? $list[$value] : '';
    }




}
