<?php

namespace app\admin\controller\user;

use app\common\controller\Backend;

/**
 * 会员管理
 *
 * @icon fa fa-circle-o
 */
class Userbb extends Backend
{

    /**
     * Userbb模型对象
     * @var \app\admin\model\Userbb
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Userbb;
        $this->view->assign("genderList", $this->model->getGenderList());
        $this->view->assign("isVipList", $this->model->getIsVipList());
        $this->view->assign("isAuthList", $this->model->getIsAuthList());
        $this->view->assign("firstRechargeList", $this->model->getFirstRechargeList());
        $this->view->assign("brandNewList", $this->model->getBrandNewList());
        $this->view->assign("girlSwitchList", $this->model->getGirlSwitchList());
        $this->view->assign("topSwitchList", $this->model->getTopSwitchList());
        $this->view->assign("nearbyHideList", $this->model->getNearbyHideList());
        $this->view->assign("distanceHideList", $this->model->getDistanceHideList());
        $this->view->assign("haveExitList", $this->model->getHaveExitList());
        $this->view->assign("imgAuthList", $this->model->getImgAuthList());
        $this->view->assign("videoAuthList", $this->model->getVideoAuthList());
        $this->view->assign("foreverOnList", $this->model->getForeverOnList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    
    

}
