<?php

namespace app\api\controller;

use app\common\controller\Api;

use think\Request;
use think\Db;
use think\Config;
//use app\api\library\IaiClient;


use TencentCloud\Cvm\*********\CvmClient;
use TencentCloud\Cvm\*********\Models\DescribeInstancesRequest;
use TencentCloud\Common\Exception\TencentCloudSDKException;
use TencentCloud\Common\Credential;


class Img2 extends Api{
    
    protected $noNeedLogin = [''];
    protected $noNeedRight = ['*'];
    
    
    public function get_token(){
        // $cred = new Credential("AKIDOdmYFnHestgKuPiLzsroWNZjnhYFnETb", "");
        // 需要设置环境变量 TENCENTCLOUD_SECRET_ID，值为示例的 AKIDz8krbsJ5yKBZQpn74WFkmLPx3*******
        $secretId = "";//getenv("TENCENTCLOUD_SECRET_ID");
        // 需要设置环境变量 TENCENTCLOUD_SECRET_KEY，值为示例的 Gu5t9xGARNpq86cd98joQYCN3*******
        $secretKey = "";//getenv("TENCENTCLOUD_SECRET_KEY");
        $host = "iai.tencentcloudapi.com";
        $service = "iai";
        $version = "2020-03-03";
        $action = 'DetectLiveFaceAccurate';//"DescribeInstances";
        $region = "ap-nanjing";
        $timestamp = time();
        // $timestamp = 1551113065;
        $algorithm = "TC3-HMAC-SHA256";
        
        // step 1: build canonical request string
        $httpRequestMethod = "POST";
        $canonicalUri = "/";
        $canonicalQueryString = "";
        $canonicalHeaders = "content-type:application/json; charset=utf-8\nhost:".$host."\nx-tc-action:".strtolower($action)."\n";
        $signedHeaders = "content-type;host;x-tc-action";
        $payload = '{}';//'{"Limit": 1, "Filters": [{"Values": ["\u672a\u547d\u540d"], "Name": "instance-name"}]}';
        $hashedRequestPayload = hash("SHA256", $payload);
        $canonicalRequest = $httpRequestMethod."\n"
            .$canonicalUri."\n"
            .$canonicalQueryString."\n"
            .$canonicalHeaders."\n"
            .$signedHeaders."\n"
            .$hashedRequestPayload;
        // echo $canonicalRequest.PHP_EOL;
        
        // step 2: build string to sign
        $date = gmdate("Y-m-d", $timestamp);
        $credentialScope = $date."/".$service."/tc3_request";
        $hashedCanonicalRequest = hash("SHA256", $canonicalRequest);
        $stringToSign = $algorithm."\n"
            .$timestamp."\n"
            .$credentialScope."\n"
            .$hashedCanonicalRequest;
        // echo $stringToSign.PHP_EOL;
        
        // step 3: sign string
        $secretDate = hash_hmac("SHA256", $date, "TC3".$secretKey, true);
        $secretService = hash_hmac("SHA256", $service, $secretDate, true);
        $secretSigning = hash_hmac("SHA256", "tc3_request", $secretService, true);
        $signature = hash_hmac("SHA256", $stringToSign, $secretSigning);
        // echo $signature.PHP_EOL;
        
        // step 4: build authorization
        $authorization = $algorithm
            ." Credential=".$secretId."/".$credentialScope
            .", SignedHeaders=content-type;host, Signature=".$signature;
        // echo $authorization.PHP_EOL;

        $curl = "curl -X POST https://".$host
            .' -H "Authorization: '.$authorization.'"'
            .' -H "Content-Type: application/json; charset=utf-8"'
            .' -H "Host: '.$host.'"'
            .' -H "X-TC-Action: '.$action.'"'
            .' -H "X-TC-Timestamp: '.$timestamp.'"'
            .' -H "X-TC-Version: '.$version.'"'
            .' -H "X-TC-Region: '.$region.'"'
            ." -d '".$payload."'";
        //echo $curl.PHP_EOL;
        exec($curl,$arr);
        dump(json_decode($arr[0],true));
        
    }
    
    function get_Score($Url){
        // 需要设置环境变量 TENCENTCLOUD_SECRET_ID，值为示例的 AKIDz8krbsJ5yKBZQpn74WFkmLPx3*******
        $secretId = "";//getenv("TENCENTCLOUD_SECRET_ID");
        // 需要设置环境变量 TENCENTCLOUD_SECRET_KEY，值为示例的 Gu5t9xGARNpq86cd98joQYCN3*******
        $secretKey = "";//getenv("TENCENTCLOUD_SECRET_KEY");
        $param["Nonce"] = rand();
        $param["Timestamp"] = time();
        $param["Region"] = "ap-chengdu";
        $param["SecretId"] = $secretId;
        $param["Version"] = "2020-03-03";
        $param["Action"] = "DetectLiveFaceAccurate";
        // $param["InstanceIds.0"] = "ins-09dx96dg";
        // $param["Limit"] = 20;
        // $param["Offset"] = 0;
        $param['Url'] = $Url;
        ksort($param);
        
        $signStr = "GETiai.tencentcloudapi.com/?";
        foreach ( $param as $key => $value ) {
            $signStr = $signStr . $key . "=" . $value . "&";
        }
        $signStr = substr($signStr, 0, -1);
        
        $signature = base64_encode(hash_hmac("sha1", $signStr, $secretKey, true));
        //echo $signature.PHP_EOL;
        //need to install and enable curl extension in php.ini
        $param["Signature"] = $signature;
        $url = "https://iai.tencentcloudapi.com/?".http_build_query($param);
        //echo $url.PHP_EOL;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
        $output = curl_exec($ch);
        curl_close($ch);
        $arr = json_decode($output,true);
        return $arr;
    }
    
    public function check_user_img(Request $req){
        $user_id = $this->auth->id;
        $txy_times = $this->auth->txy_times;
        if($txy_times<=0){$this->error('今日剩余认证次数已用完','');}
        $img = $req->param('img/s','');
        if($img==''){$this->error('空图');}
        $true_img = get_avatar($img);
        // 实例化一个证书对象，入参需要传入腾讯云账户secretId，secretKey
        //$cred = new Credential(getenv("TENCENTCLOUD_SECRET_ID"), getenv("TENCENTCLOUD_SECRET_KEY"));
        $rep = $this->get_Score($true_img);
        if(isset($rep['Response']['Error'])){
            $this->success('',['success'=>0,'msg'=>$rep['Response']['Error']['Message']]);
        }else{
            $Score = $rep['Response']['Score'];
        }
        
        $min = Config::get('site.living_organism_score');
        if($min>$Score){
            Db::name('user')->where('id',$user_id)->dec('txy_times',1)->update();
            //$this->error('请上传现拍照片',$Score);
            $this->success('',['success'=>0,'msg'=>'活体程度'.$Score.'分，小于限值'.$min]);
        }
        $save = [];
        $save['img_auth'] = '1';
        $save['auth_image'] = $img;
        $R = Db::name('user')->where('id',$user_id)->update($save);
        if($R!==false){
            Db::name('user')->where('id',$user_id)->dec('txy_times',1)->update();
            $this->success('',['success'=>1,'msg'=>'']);
        }else{
            Db::name('user')->where('id',$user_id)->dec('txy_times',1)->update();
            $this->error('操作失败',$Score);
        }
    }

}
