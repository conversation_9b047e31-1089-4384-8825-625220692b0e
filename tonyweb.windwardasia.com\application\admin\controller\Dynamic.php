<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use Exception;
use app\common\library\Redis;
use think\Env;
use think\Queue;
use think\Request;


/**
 * 帖子动态
 *
 * @icon fa fa-circle-o
 */
class Dynamic extends Backend
{

    /**
     * Dynamic模型对象
     * @var \app\admin\model\Dynamic
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Dynamic;
        $this->view->assign("isHotList", $this->model->getIsHotList());
        $this->view->assign("haveSourceList", $this->model->getHaveSourceList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        $req = Request::instance();
        $user_id = $req->param('user_id','');
        
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user'])
                    ->where(function($sql)use($user_id){
                        if($user_id){
                            $sql->where('user_id',$user_id);
                        }
                        return $sql;
                    })
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);
            $prefix = Env::get('redis.redis_prefix');
            $redis = (new Redis())->handler;
            foreach ($list as $row) {
                $row->visible(['id','content','createtime','great_num','look_num','comments_num','location','status','have_source']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['nickname']);
				$great_exist = $redis->get($prefix.'dynamic_greating_'.$row->id);
				$cmt_exist = $redis->get($prefix.'dynamic_cmting_'.$row->id);
				if($great_exist=='1'){$row->dynamic_greating = 1;}else{$row->dynamic_greating = 0;}
				if($cmt_exist=='1'){$row->dynamic_cmting = 1;}else{$row->dynamic_cmting = 0;}
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
    
    
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
                Db::name('dynamic_comments')->where('dynamic_id',$item->id)->delete();
                Db::name('dynamic_great')->where('dynamic_id',$item->id)->delete();
                Db::name('dynamic_img')->where('dynamic_id',$item->id)->delete();
                Db::name('user_base')->where('user_id',$item->user_id)
                ->dec('great_num',$item->great_num)->dec('comment_num',$item->comments_num)
                ->dec('dynamic_num',1)->update();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }
    
    
    public function import(){
        // ini_set('memory_limit','128M');
        ini_set('memory_limit',-1); //没有内存限制
        // dump($this->request->request());
        $file = $this->request->request('file');
        if (!$file) {
            $this->error(__('Parameter %s can not be empty', 'file'));
        }
        
        $imageFilePath = './uploads/'.date('Ymd').'/'; //图片本地存储的路径
        if (!file_exists($imageFilePath)) { //如果目录不存在则递归创建
            mkdir($imageFilePath, 0777, true);
        }
        // halt($imageFilePath);
        //加载打开文件 $file
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
        //这一行不要写，写了这个是获取不到图片这些数据的
        //$reader->setReadDataOnly(TRUE);
        $spreadsheet = $reader->load('.'.$file);
        $objWorksheet = $spreadsheet->getSheet(0);
        $data = $objWorksheet->toArray();//dump($data);
        // $UPOBj = new \addons\qiniu\controller\Upl();
        //遍历
        foreach ($objWorksheet->getDrawingCollection() as &$drawing) {
            list($startColumn, $startRow) = Coordinate::coordinateFromString($drawing->getCoordinates());//dump($startColumn);第几列    dump($startRow);第几行
            $imageFileName = $drawing->getCoordinates() . mt_rand(10000, 99999);
        
            switch ($drawing->getExtension()) {
                case 'jpg':
                case 'jpeg':
                    //加了时间戳是为了不让文件名重复
                    $imageFileName .= '_'.time().'.jpg';
                    $source = imagecreatefromjpeg($drawing->getPath());
                    imagejpeg($source, $imageFilePath . $imageFileName);$source = null;
                    // $UPOBj->upload_excel($imageFilePath . $imageFileName);
                    break;
                case 'gif':
                    $imageFileName .= '_'.time().'.gif';
                    $source = imagecreatefromgif($drawing->getPath());
                    imagegif($source, $imageFilePath . $imageFileName);$source = null;
                    break;
                case 'png':
                    $imageFileName .= '_'.time().'.png';
                    $source = imagecreatefrompng($drawing->getPath());
                    imagepng($source, $imageFilePath. $imageFileName);$source = null;
                    break;
                 case 'webp':
                    $imageFileName .= '_'.time().'.webp';
                    $source = imagecreatefromwebp($drawing->getPath());
                    imagewebp($source, $imageFilePath. $imageFileName);$source = null;
                    break;
            }
            $startColumn = ABC2decimal($startColumn);
            //把保存成链接的文件放回拿到的数据中
            // $data[$startRow-1][$startColumn] = $this->request->domain() . $path . $imageFileName;
            $data[$startRow-1][$startColumn] = $imageFilePath . $imageFileName;
            $drawing = $source = $imageFileName = null;
            // $UPOBj->upload_excel();
        }
        
        // unset($drawing);
        
        $prefix = Env::get('redis.redis_prefix');
        $redis = (new Redis())->handler;
        $time = time();
        $imgs = '';
        foreach($data as $k=>$v){
            if($k==0){continue;}
            $location = $v[0];
            $date = $v[1];
            $times = $v[2];
            $content = $v[3];

            if($v[4]){$imgs_arr[] = substr($v[4],1);}
            if($v[5]){$imgs_arr[] = substr($v[5],1);}
            if($v[6]){$imgs_arr[] = substr($v[6],1);}
            if($v[7]){$imgs_arr[] = substr($v[7],1);}
            if($v[8]){$imgs_arr[] = substr($v[8],1);}
            if($v[9]){$imgs_arr[] = substr($v[9],1);}
            if($v[10]){$imgs_arr[] = substr($v[10],1);}
            if($v[11]){$imgs_arr[] = substr($v[11],1);}
            if($v[12]){$imgs_arr[] = substr($v[12],1);}
            if(!empty($imgs_arr)){$imgs = implode(',',$imgs_arr);}else{$imgs = '';}
            $img_url = [];
            if($imgs_arr){
//                $url = 'https://tangx.jiangkukeji.cn/index/upload_imgs';
                foreach ($imgs_arr as $key=>$value){
                    $url = 'https://tangxweb.tangxingongyuan.com/api/index/imgs_upload?imgs='.$value;
                    $R = \fast\Http::get($url);
                    $data = json_decode($R,true);
                    if ($data){
                        $img_url[] = $data['data']['key'];
                    }else{
                        continue;
                    }

                }

            }
            $arr = ['lng'=>'','lat'=>''];
            $info = \fast\Http::get("https://restapi.amap.com/v3/geocode/geo?address={$location}&key=e16a5f8e63362910e0005c54a50eba12");
            $city = json_decode($info,true);
            if ($city && isset($city['status']) && $city['status'] == 1 && isset($city['geocodes']) && $city['geocodes']){
                $locations = $city['geocodes'][0]['location'];
                $str_arr = explode(',',$locations);
                $arr = ['lng'=>$str_arr[0],'lat'=>$str_arr[1]];
            }
            $img_url = implode(',',$img_url);
            $data = [
                'location'=>$location,
                'content'=>$content,
                'lng'=>$arr['lng'],
                'lat'=>$arr['lat'],
                'date'=>$date,
                'times'=>$times,
                'imgs'=>$img_url,
            ];
            $start = strtotime($date);
            if($start==false){$start = time();}
            $end = $start+86400;
            if($times==false){$times = 1;}
            $arr = rand_time_point($start,$end,$times);
            $job =\app\api\job\Dynamic::class;
            if(!empty($arr)){
                foreach($arr as $unix_time){

                    $data['createtime'] = $unix_time;
                    if($unix_time<=$time){//立即发布
                        Queue::push($job,['data'=>$data]);
                    }else{//延时发布
                        $diff = $unix_time-$time;
                        Queue::later($diff,$job,['data'=>$data]);
                    }

                }
            }
            /*$key = 'dynamic_warehouse_'.$time.'_'.$k;
            $redis->hmset($prefix.$key,$data);
            $redis->rpush($prefix.'crontab_dynamic_warehouse',$key);*/

            adlog("是不是",$img_url);
            
        }

        $this->success('导入成功');
    }
    
    
    
    
    public function dynamic_greating(){
        $dynamic_id = $this->request->param('dynamic_id');
        if(!$dynamic_id){$this->error('参数错误');}
        $prefix = Env::get('redis.redis_prefix');
        $redis = (new Redis())->handler;
        $redis->rpush($prefix.'crontab_dynamic_great',strval($dynamic_id));
        $redis->setex($prefix.'dynamic_greating_'.$dynamic_id,86400,'1');
        $this->success('点赞任务添加成功');
    }
    
    public function dynamic_cmting(){
        $dynamic_id = $this->request->param('dynamic_id');
        if(!$dynamic_id){$this->error('参数错误');}
        $prefix = Env::get('redis.redis_prefix');
        $redis = (new Redis())->handler;
        $redis->rpush($prefix.'crontab_dynamic_cmt',strval($dynamic_id));
        $redis->setex($prefix.'dynamic_cmting_'.$dynamic_id,86400,'1');
        $this->success('评论任务添加成功');
    }
    
    
}
