<?php

namespace app\admin\model;

use think\Model;


class Dynamic extends Model
{

    

    

    // 表名
    protected $name = 'dynamic';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'is_hot_text',
        'have_source_text',
        'status_text',
        'dynamic_greating',
        'dynamic_cmting'
    ];
    

    
    public function getIsHotList()
    {
        return ['0' => __('Is_hot 0'), '1' => __('Is_hot 1')];
    }

    public function getHaveSourceList()
    {
        return ['0' => __('Have_source 0'), '1' => __('Have_source 1'), '2' => __('Have_source 2')];
    }

    public function getStatusList()
    {
        return [ '1' => __('Status 1'), '2' => __('Status 2')];//'0' => __('Status 0'),
    }


    public function getIsHotTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_hot']) ? $data['is_hot'] : '');
        $list = $this->getIsHotList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getHaveSourceTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['have_source']) ? $data['have_source'] : '');
        $list = $this->getHaveSourceList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
