<?php

namespace app\admin\model;

use think\Model;


class DynamicImg extends Model
{

    

    

    // 表名
    protected $name = 'dynamic_img';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'source_type_text',
        'is_qualified_text',
        'ext'
    ];
    

    
    public function getSourceTypeList()
    {
        return ['1' => __('Source_type 1'), '2' => __('Source_type 2')];
    }

    public function getIsQualifiedList()
    {
        return ['1' => __('Is_qualified 1'), '0' => __('Is_qualified 0')];
    }


    public function getSourceTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['source_type']) ? $data['source_type'] : '');
        $list = $this->getSourceTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getIsQualifiedTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_qualified']) ? $data['is_qualified'] : '');
        $list = $this->getIsQualifiedList();
        return isset($list[$value]) ? $list[$value] : '';
    }




}
