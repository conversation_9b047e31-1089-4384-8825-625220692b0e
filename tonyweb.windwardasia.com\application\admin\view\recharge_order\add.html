<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Good_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-good_id" data-rule="required" data-source="good/index" class="form-control selectpage" name="row[good_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_sn')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_sn" class="form-control" name="row[order_sn]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" min="0" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" min="0" class="form-control" step="0.01" name="row[amount]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Paytype')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-paytype" class="form-control selectpicker" name="row[paytype]">
                {foreach name="paytypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payamount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-payamount" min="0" class="form-control" step="0.01" name="row[payamount]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Diamond')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-diamond" min="0" class="form-control" name="row[diamond]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Paytime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-paytime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[paytime]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Memo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-memo" class="form-control" name="row[memo]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="0"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Out_sn')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-out_sn" class="form-control" name="row[out_sn]" type="text">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
