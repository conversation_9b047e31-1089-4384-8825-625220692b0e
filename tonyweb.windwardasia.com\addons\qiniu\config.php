<?php

return [
    [
        'name' => 'accessKey',
        'title' => 'accessKey',
        'type' => 'string',
        'content' => [],
        'value' => '95iHvQ24Y_b3JlmdmAWLLYWlouCZ_pWMXz2c0YtQ',
        'rule' => 'required',
        'msg' => '',
        'tip' => '请在个人中心 > 密钥管理中获取 > AK',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'secretKey',
        'title' => 'secretKey',
        'type' => 'string',
        'content' => [],
        'value' => 'yy8faiGmteRiUrDXNN2D3EOii3ZpW9k-zB_ODoTc',
        'rule' => 'required',
        'msg' => '',
        'tip' => '请在个人中心 > 密钥管理中获取 > SK',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'bucket',
        'title' => 'bucket',
        'type' => 'string',
        'content' => [],
        'value' => '0520hwsj',
        'rule' => 'required;bucket',
        'msg' => '',
        'tip' => '存储空间名称',
        'ok' => '',
        'extend' => 'data-rule-bucket="[/^[0-9a-z_\\-]{3,63}$/, \'请输入正确的Bucket名称\']"',
    ],
    [
        'name' => 'uploadurl',
        'title' => '上传接口地址',
        'type' => 'select',
        'content' => [
            'https://upload-z0.qiniup.com' => '华东 https://upload-z0.qiniup.com',
            'https://upload-z1.qiniup.com' => '华北 https://upload-z1.qiniup.com',
            'https://upload-z2.qiniup.com' => '华南 https://upload-z2.qiniup.com',
            'https://upload-na0.qiniup.com' => '北美 https://upload-na0.qiniup.com',
            'https://upload-as0.qiniup.com' => '东南亚 https://upload-as0.qiniup.com',
            'https://up-cn-east-2.qiniup.com' => 'https://up-cn-east-2.qiniup.com',
        ],
        'value' => 'https://upload-na0.qiniup.com',
        'rule' => 'required',
        'msg' => '',
        'tip' => '推荐选择最近的地址',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'cdnurl',
        'title' => 'CDN地址',
        'type' => 'string',
        'content' => [],
        'value' => 'https://img.tomeetr.com',
        'rule' => 'required;cdnurl',
        'msg' => '',
        'tip' => '未绑定CDN的话可使用七牛分配的测试域名',
        'ok' => '',
        'extend' => 'data-rule-cdnurl="[/^http(s)?:\\/\\/.*$/, \'必需以http(s)://开头\']"',
    ],
    [
        'name' => 'uploadmode',
        'title' => '上传模式',
        'type' => 'select',
        'content' => [
            'client' => '客户端直传(速度快,无备份)',
            'server' => '服务器中转(占用服务器带宽,可备份)',
        ],
        'value' => 'client',
        'rule' => '',
        'msg' => '',
        'tip' => '启用服务器中转时务必配置操作员和密码',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'serverbackup',
        'title' => '服务器中转模式备份',
        'type' => 'radio',
        'content' => [
            1 => '备份(附件管理将产生2条记录)',
            0 => '不备份',
        ],
        'value' => '0',
        'rule' => '',
        'msg' => '',
        'tip' => '服务器中转模式下是否备份文件',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'savekey',
        'title' => '保存文件名',
        'type' => 'string',
        'content' => [],
        'value' => '/uploads/$(year)$(mon)$(day)/$(etag)$(ext)',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'expire',
        'title' => '上传有效时长',
        'type' => 'string',
        'content' => [],
        'value' => '600',
        'rule' => 'required',
        'msg' => '',
        'tip' => '用户停留页面上传有效时长，单位秒',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'maxsize',
        'title' => '最大可上传',
        'type' => 'string',
        'content' => [],
        'value' => '200M',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'mimetype',
        'title' => '可上传后缀格式',
        'type' => 'string',
        'content' => [],
        'value' => 'jpg,png,bmp,jpeg,gif,zip,rar,xls,xlsx,vmwarevm,sav,svga,mp4',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'multiple',
        'title' => '多文件上传',
        'type' => 'radio',
        'content' => [
            1 => '开启',
            0 => '关闭',
        ],
        'value' => '0',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'thumbstyle',
        'title' => '缩略图样式',
        'type' => 'string',
        'content' => [],
        'value' => '',
        'rule' => '',
        'msg' => '',
        'tip' => '用于后台列表缩略图样式，可使用：?imageView2/2/w/120/h/90/q/80',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'chunking',
        'title' => '分片上传',
        'type' => 'radio',
        'content' => [
            1 => '开启',
            0 => '关闭',
        ],
        'value' => '1',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'chunksize',
        'title' => '分片大小',
        'type' => 'number',
        'content' => [],
        'value' => '4194304',
        'rule' => 'required',
        'msg' => '',
        'tip' => '固定大小,不能修改',
        'ok' => '',
        'extend' => 'readonly',
    ],
    [
        'name' => 'syncdelete',
        'title' => '附件删除时是否同步删除文件',
        'type' => 'bool',
        'content' => [],
        'value' => '1',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'apiupload',
        'title' => 'API接口使用云存储',
        'type' => 'bool',
        'content' => [],
        'value' => '1',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'noneedlogin',
        'title' => '免登录上传',
        'type' => 'checkbox',
        'content' => [
            'api' => 'API',
            'index' => '前台',
            'admin' => '后台',
        ],
        'value' => 'api',
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'uploadmodule',
        'title' => '上传开启模块',
        'type' => 'checkbox',
        'content' => [
            'api' => 'API',
            'index' => '前台',
            'admin' => '后台',
        ],
        'value' => 'api,index,admin',
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
];
