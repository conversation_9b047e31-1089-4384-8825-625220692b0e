<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>
        </div>
    </div>

   <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
            {foreach name="typeList" item="vo"}
            <label for="row[type]-{$key}"><input id="row[type]-{$key}" name="row[type]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Good_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-good_code" class="form-control" name="row[good_code]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('会员名称（简体）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text">
        </div>
    </div>
   <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('会员名称（韩文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ko_name" class="form-control" name="row[ko_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('会员名称（日文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ja_name" class="form-control" name="row[ja_name]" type="text">
        </div>
    </div>
    -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('会员名称（繁体）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hk_name" class="form-control" name="row[hk_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('会员名称（英文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-en_name" class="form-control" name="row[en_name]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Old_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-old_price" class="form-control" step="0.01" name="row[old_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Now_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-now_price" class="form-control" step="0.01" name="row[now_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Month')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-month" class="form-control" name="row[month]" type="number" value="1">
        </div>
    </div>
    <!--<div class="form-group"  data-favisible="status=1">
        <label class="control-label col-xs-12 col-sm-2">{:__('赠送翻译次数')}:</label>
        <div class="col-xs-12 col-sm-8">
        </div>
    </div>-->
    <input id="c-translation" class="form-control" name="row[translation]" type="hidden" value="99999999">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Unit')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-unit" class="form-control selectpicker" name="row[unit]">
                {foreach name="unitList" item="vo"}
                    <option value="{$key}" {in name="key" value="month"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Switch')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-switch" class="form-control selectpicker" name="row[switch]">
                {foreach name="switchList" item="vo"}
                    <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
