<?php

return [
    'Id'                  => 'ID',
    'Type_status'         => '系统类型',
    'Type_status ios'     => '苹果',
    'Type_status android' => '安卓',
    'Oldversion'          => '旧版本号',
    'Newversion'          => '版本号',
    'Packagesize'         => '包大小',
    'Content'             => '升级内容',
    'Downloadurl'         => '下载地址',
    'On_switch'           => '上线',
    'On_switch 1'         => '上线',
    'On_switch 0'         => '下线',
    'Createtime'          => '创建时间',
    'Updatetime'          => '更新时间',
    'Weigh'               => '排序',
    'Status'=>'上架状态',
    'Status 1'=>'开发中',
    'Status 2'=>'审核中',
    'Status 3'=>'上架成功',
    'platform'=>'应用平台',
    'platform 1'=>'华为',
    'platform 2'=>'小米',
    'platform 3'=>'oppo',
    'platform 4'=>'vivo',
    'platform 5'=>'应用宝',
    'platform 6'=>'AppStore',
];
