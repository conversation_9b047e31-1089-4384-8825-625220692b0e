<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\User;
use think\Db;

/**
 * 天使币投诉记录
 *
 * @icon fa fa-circle-o
 */
class UserScoreAudit extends Backend
{

    /**
     * UserScoreAudit模型对象
     * @var \app\admin\model\UserScoreAudit
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\UserScoreAudit;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->with(['user'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {

                $row->getRelation('user')->visible(['nickname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    public function audit($ids, $type)
    {
        $info         = $this->model->get($ids);
        $info->status = '2';
        if ($type == 1) {
            $info->money_to = '退还给投诉人';
            $this->score($info->num, $info->user_id, '消费退还', 3);
            $to_user_info = Db::name('user')->where('id', $info->to_user_id)->find();
            $after        = $to_user_info['ice_score'] - $info->num;
            Db::name('user')->where('id', $info->to_user_id)->update(['ice_score' => $after]);
        } else {
            $info->money_to = '退还给被投诉人';
            Db::name('user_score_log')->where('log_id', $info->user_score_log_id)->update(['is_jubao' => 2]);
        }
        $info->save();
        $this->success('操作成功');
    }

    /**
     * 变更会员积分
     * @param int $score 积分
     * @param int $user_id 会员ID
     * @param string $memo 备注
     */
    public function score($score, $user_id, $memo, $type = 1, $endtime = 0, $is_jubao = 2, $from_uid = 0, $log_id = 0)
    {

        $user   = Db::name('user')->where('id', $user_id)
            ->find();
        $before = 0;
        $after  = 0;
        if ($user && $score != 0) {
            switch ($type) {
                case 1:
                    $cha = $user['from_score'] + $score;
                    if ($cha > 0) {
                        $from_score  = $cha;
                        $after_score = $user['score'];
                    } else {
                        $from_score  = 0;
                        $after_score = $user['score'] + $cha;
                    }
                    Db::name('user')->where('id', $user_id)->update(['score' => $after_score, 'from_score' => $from_score]);
                    break;
                case 2:
                    $before = $user['ice_score'];
                    $after  = $user['ice_score'] + $score;
                    Db::name('user')->where('id', $user_id)->update(['ice_score' => $after]);
                    break;
                case 3:
                    $before = $user['score'];
                    $after  = $user['score'] + $score;
                    //更新会员信息
                    Db::name('user')->where('id', $user_id)->update(['score' => $after]);
                    break;
                default:
                    break;
            }
            //写入日志
            $id = Db::name('user_score_log')->insertGetId([
                'user_id'    => $user_id,
                'score'      => $score,
                'before'     => $before,
                'after'      => $after,
                'memo'       => $memo,
                'createtime' => time(),
                'type'       => $type,
                'endtime'    => $endtime,
                'is_jubao'   => $is_jubao,
                'from_uid'   => $from_uid,
                'log_id'     => $log_id,
                'is_ice'     => 2
            ]);
            return $id;

        }
    }


}
