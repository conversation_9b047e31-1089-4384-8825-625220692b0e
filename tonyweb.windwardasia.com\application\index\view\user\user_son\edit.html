<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    {:token()}
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('p_user')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-p_user" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[p_user]" type="text" value="{$row.p_user|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('is_partner')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_partner" class="form-control selectpicker" name="row[is_partner]">
                {foreach name="IsPartnerList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.is_partner"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('partner_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-partner_id"  data-source="partner_level/index" data-field="name" class="form-control selectpage" name="row[partner_id]" type="text" value="{$row.partner_id|htmlentities}">
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
