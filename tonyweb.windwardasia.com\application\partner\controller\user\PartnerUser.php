<?php

namespace app\partner\controller\user;

use app\admin\model\user\UserPartnerStatus;
use app\common\controller\PartnerBase;
use app\common\library\Auth;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use app\common\library\Redis;
use think\Env;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Session;

/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class PartnerUser extends PartnerBase
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('User');
        
        $this->view->assign("genderList", $this->model->getGenderList());
        $this->view->assign("isVipList", $this->model->getIsVipList());
        $this->view->assign("isAuthList", $this->model->getIsAuthList());
        $this->view->assign("firstRechargeList", $this->model->getFirstRechargeList());
        $this->view->assign("brandNewList", $this->model->getBrandNewList());
        $this->view->assign("girlSwitchList", $this->model->getGirlSwitchList());
        $this->view->assign("topSwitchList", $this->model->getTopSwitchList());
        $this->view->assign("nearbyHideList", $this->model->getNearbyHideList());
        $this->view->assign("distanceHideList", $this->model->getDistanceHideList());
        $this->view->assign("haveExitList", $this->model->getHaveExitList());
        $this->view->assign("imgAuthList", $this->model->getImgAuthList());
        $this->view->assign("videoAuthList", $this->model->getVideoAuthList());
        $this->view->assign("foreverOnList", $this->model->getForeverOnList());
        $this->view->assign('IsPartnerList', $this->model->getIsPartnerList());
        $this->view->assign("partnerStatusList", $this->model->getPartnerStatusList());
        
        $this->view->assign("applyPartnerStatusList", $this->model->getApplyPartnerStatusList());
    }

    /**
     * 查看
     */
    public function index()
    {   $p_user = $this->request->param('p_user');
            $p_user = $this->request->param('p_user','');
            $user_id = Session::get('admin')['id'];
            if(!$p_user){$p_user = $user_id;}
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with('group')
                ->where(function($sql)use($p_user){
                    if($p_user){
                        $sql->where('p_user',$p_user);
                    }
                    return $sql;
                })
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
            $li = Db::name('partner_level')->column('id,name,rate');
            foreach ($list as $k => &$v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
                if($v->p_user){
                    $v->p_user_nickname = $this->model->where('id',$v->p_user)->value('nickname');
                }else{
                    $v->p_user_nickname = '';
                }
                if($v->auth_image==false){$v->auth_image = '';}
                if($v->auth_file==false){$v->auth_file = '';}
                if($v->partner_id){
                    if(isset($li[$v->partner_id])){
                        $v->partner_name = $li[$v->partner_id]['name'];
                        $v->partner_rate = $li[$v->partner_id]['rate'];
                    }else{
                        $v->partner_name = '无';
                        $v->partner_rate = '0';
                    }
                }else{
                    $v->partner_name = '无';
                    $v->partner_rate = '0';
                }
            }//dump( collection($list)->toArray());
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        $this->view->assign('p_user', $p_user);
        $this->view->assign('user_id', $user_id);
        return $this->view->fetch();
    }
    public function data($ids){
        $this->view->assign("id", $ids);
        return $this->view->fetch();
    }
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        return parent::add();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
        // return parent::edit($ids);
        return $this->treu_edit($ids);
    }


    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function treu_edit($ids = null)
    {
        $row = $this->model->get($ids);
        $yuan_status = $row->status;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }else{
            // user表,normal,hidden
            if($yuan_status!=$params['status'] && $params['status']=='hidden'){
                (new \app\common\library\token\driver\Redis())->clear($ids);
            }
        }
        $this->success();
    }



    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }
    
    
    /**
     * 详情
     */
    public function detail($ids){
        $row = $this->model
        ->field('id,nickname,mobile,gender,birthday,bio,score,money,son_num,p_user,son_money,forever_on,wx_id,height,weight,master_city,point_city,is_vip,vip_end_time,is_auth,first_recharge,brand_new,girl_switch,characters,relation,occupation,label,nearby_hide,distance_hide,unlock_chat,unlock_weixin,unlock_chatup,txy_times,need_edit,true_name,true_code,wechat_name,alipay_name,wechat,alipay,wx_openid,img_auth,video_auth,video_failure,successions,maxsuccessions,prevtime,logintime,loginip,loginfailure,joinip,jointime,createtime,updatetime,top_switch,exit_time,have_exit')
        ->where(['id' => $ids])->find();
        if (!$row){$this->error(__('No Results were found'));}else{
            $row = $row->toArray();
            $row2 = [];
            
            $prefix = Env::get('redis.redis_prefix');
            $redis = (new Redis())->handler;
            $data = $redis->hgetall("{$prefix}global_select_code");
            
            foreach ($row as $x=>&$y){
                $x = str_replace('_text','',$x);
                if(in_array($x,['characters','relation','occupation','label'])){
                    if($y==false){$y = '';}else{$y = strtr($y,$data);}
                }elseif(in_array($x,['createtime','updatetime'])){
                    $y = date('Y-m-d H:i:s',$y);
                }
                $row2[$x] = $y;
            }
            
            $row = $row2;
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }


    public function edit_partner($ids = null){
        $field = ['partner_id','is_partner','py_user.id','p_user','partner_status'];
        $row = Db::name('user')->alias('a')
        ->join('partner_level b','b.id=a.partner_id','left')
        ->field($field)->where('a.id',$ids)->find();
        if($row){
            if($row['is_partner']=='0'){
                $row['is_partner_attr'] = '普通';
            }elseif($row['is_partner']=='1'){
                $row['is_partner_attr'] = '推广人';
            }

        }
        $this->view->assign('row_json', json_encode($row));
        // $li = Db::name('partner_level')->field('id,name,rate')->select();
        // $li = [0=>['id'=>'0','name'=>'无分佣率','rate'=>'']]+$li;
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            
            // $this->view->assign('partner_list', $li);
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');

        $params['partner_status'] = 1;
        if($params['partner_id']){
            //上级。下级最大值
            $li = Db::name('partner_level')->column('id,rate');
            $up = Db::name('user')->alias('a')
            ->join('user b','b.id=a.p_user','inner')
            ->join('partner_level c','c.id=b.partner_id','inner')
            ->field('b.partner_id,a.id as sid,b.id pid,rate')->where('a.id',$ids)
            ->find();
            if(!$up){$this->error('已超出您的最大权限值');}
            if($up['rate']<=$li[$params['partner_id']]){
                $this->error('已超出您的最大权限值');
            }
            
            $son_ids = Db::name('user')->where('p_user',$ids)->where('partner_id','<>',0)->column('partner_id');
            if(!empty($son_ids)){
                foreach ($son_ids as $y){
                    if($li[$y]>= $li[$params['partner_id']]){
                        $this->error('已超出您的最大权限值');
                    }
                }
            }
            
        }

        // if($row['is_partner']!=$params['is_partner'] && $params['is_partner']=='1' ){//&& $params['partner_status']=='2'//$row['partner_id']!=$params['partner_id'] || 
        //     $params['partner_status'] = '2';
        //     $params['is_partner_time'] = time();
        // }elseif($row['is_partner']!=$params['is_partner'] && $params['is_partner']=='0' ){//&& $params['partner_status']=='2'//$row['partner_id']!=$params['partner_id'] || 
        //     $params['partner_status'] = '0';
        //     $params['is_partner_time'] = 0;
        // }
        $result = false;
        Db::startTrans();
        try {
            if($params['is_partner']=='0'){
                $is_partner = '普通';
            }elseif($params['is_partner']=='1'){
                $is_partner = '推广人';
            }
            (new \app\admin\model\user\UserPartnerStatus)->save([
                'user_id'       =>      $ids,
                'partner_id'    =>      $this->auth->id,
                'old_is_partner'=>      $row['is_partner_attr'],
                'new_is_partner'=>      $is_partner,
                'old_partner_level' =>  $row['partner_id'],
                'new_partner_level' =>  $params['partner_id'],
                'createtime'        =>  time(),
                'updatetime'        =>  time()
            ]);
            require_once ROOT_PATH."extend/GatewayWorker/vendor/workerman/gateway-worker/src/Protocols/GatewayProtocol.php";
            require_once ROOT_PATH."extend/GatewayWorker/vendor/workerman/gateway-worker/src/Lib/Gateway.php";
            require_once ROOT_PATH."extend/GatewayWorker/vendor/workerman/gateway-worker/src/Lib/Context.php";
            require_once ROOT_PATH."extend/GatewayWorker/vendor/workerman/gateway-worker/src/Lib/Db.php";
            require_once ROOT_PATH."extend/GatewayWorker/vendor/workerman/gateway-worker/src/Lib/DbConnection.php";
            \GatewayWorker\Lib\Gateway::$registerAddress = '127.0.0.1:1338';
            $client_id = \GatewayWorker\Lib\Gateway::getClientIdByUid('1');
            adlog("看看id",$client_id);
            if ($client_id) {
                $num = count($client_id);
                $resData = [
                    'type' => 'new_order',
                    'client_id' => $client_id[$num - 1],
                    'msg' => '有新的推广人审核，请注意查看' // 初始化房间信息
                ];
                \GatewayWorker\Lib\Gateway::sendToClient($client_id[$num - 1], json_encode($resData));
            }
            $result = true;
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    
    



    
}

