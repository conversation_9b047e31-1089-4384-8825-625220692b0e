<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id}">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text"
                   value="{$row.nickname|htmlentities}" readonly=" readonly">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('VIP')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_vip]', ['one'=>__('会员'),'three'=>__('非会员')], $row['is_vip'])}
        </div>
    </div>
    <!--<div class="form-group" data-favisible="is_vip=one">
        <label class="control-label col-xs-12 col-sm-2">{:__('会员类型')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[vip_type]', ['1'=>__('vip'), '2'=>__('vvip')], $row['vip_type'])}
        </div>
    </div>-->
    <!--<div class="form-group" data-favisible="vip_type=1">
        <label class="control-label col-xs-12 col-sm-2">{:__('翻译次数')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-translation_num" class="form-control" name="row[translation_num]" type="text"
                   value="{$row.translation_num|htmlentities}">
        </div>
    </div>-->
    <div class="form-group"  data-favisible="is_vip='one'">
        <label class="control-label col-xs-12 col-sm-2">{:__('到期时间')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vip_end_time" min="0" class="form-control datetimepicker"
                   data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[vip_end_time]" type="text"
                   value="{:$row.vip_end_time?datetime($row.vip_end_time):''}">
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>