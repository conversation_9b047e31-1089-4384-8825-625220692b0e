<style type="text/css">
    .sm-st {
        background: #fff;
        padding: 20px;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        margin-bottom: 20px;
    }

    .sm-st-icon {
        width: 60px;
        height: 60px;
        display: inline-block;
        line-height: 60px;
        text-align: center;
        font-size: 30px;
        background: #eee;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        float: left;
        margin-right: 10px;
        color: #fff;
    }

    .sm-st-info {
        padding-top: 2px;
    }

    .sm-st-info span {
        display: block;
        font-size: 24px;
        font-weight: 600;
    }

    .orange {
        background: #fa8564 !important;
    }

    .tar {
        background: #45cf95 !important;
    }

    .sm-st .green {
        background: #86ba41 !important;
    }

    .pink {
        background: #AC75F0 !important;
    }

    .yellow-b {
        background: #fdd752 !important;
    }

    .stat-elem {

        background-color: #fff;
        padding: 18px;
        border-radius: 40px;

    }

    .stat-info {
        text-align: center;
        background-color: #fff;
        border-radius: 5px;
        margin-top: -5px;
        padding: 8px;
        -webkit-box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        font-style: italic;
    }

    .stat-icon {
        text-align: center;
        margin-bottom: 5px;
    }

    .st-red {
        background-color: #F05050;
    }

    .st-green {
        background-color: #27C24C;
    }

    .st-violet {
        background-color: #7266ba;
    }

    .st-blue {
        background-color: #23b7e5;
    }

    .stats .stat-icon {
        color: #28bb9c;
        display: inline-block;
        font-size: 26px;
        text-align: center;
        vertical-align: middle;
        width: 50px;
        float: left;
    }

    .stat {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }

    .stat .value {
        font-size: 20px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
    }

    .stat .name {
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 5px 0;
    }

    .stat.lg .value {
        font-size: 26px;
        line-height: 28px;
    }

    .stat-col {
        margin:0 0 10px 0;
    }
    .stat.lg .name {
        font-size: 16px;
    }

    .stat-col .progress {
        height: 2px;
    }

    .stat-col .progress-bar {
        line-height: 2px;
        height: 2px;
    }

    .item {
        padding: 30px 0;
    }


    #statistics .panel {
        min-height: 150px;
    }

    #statistics .panel h5 {
        font-size: 14px;
    }

    table{
        width:100%;
        margin:15px 0;
        border:0;
    }
    table th{
        background-color:#00A5FF;
        color:#FFFFFF;
        font-size:0.95em;
        text-align:center;
        padding:4px;
        border-collapse:collapse;
        border: 1px solid #2087fe;
        border-width:1px 0 1px 0;
        border:2px inset #ffffff;
    }
    table td{
        font-size:0.95em;
        text-align:center;
        padding:4px;
        border-collapse:collapse;
        border: 1px solid #2087fe;
        border-width:1px 0 1px 0;
        border:2px inset #ffffff;
    }

</style>

<input type="text" class="hidden" value="{$id}">
<div class="panel panel-default panel-intro">
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade  active in" id="two">
                <div class="row">
                    <div class="col-xs-12">
<!--                        {:__('Custom zone')}-->



                        <table>
                            <thead>
                            <tr>
                                <th style="width:150px">统计/类型</th>
                                <th style="width:150px">团队总用户</th>
                                <th style="width:150px">今日新增用户</th>
                                <th style="width:150px">团队总充值</th>
                                <th style="width:150px;">今日新增充值</th>
                                <th style="width:150px;">团队总佣金</th>
                                <th style="width:150px;">今日总佣金</th>
                            </tr>
                            </thead>
                            <tbody id='default_nums'>

                            </tbody>
                        </table>
                        
                        
                        
                        <HR style="FILTER: progid:DXImageTransform.Microsoft.Shadow(color:#987cb9,direction:145,strength:15)" width="80%" color=#987cb9 SIZE=1>
                            
                            
                        <form class="form-horizontal" onsubmit="return false" style="padding-top: 2%" role="form" method="POST" action="">
                        
                            <div class="form-group" style="">
                                <label class="control-label col-xs-12 col-sm-2">查询时间起点:</label>
                                <div class="col-xs-12 col-sm-8">

                                    <input class="form-control datetimepicker"
                                           data-date-format="YYYY-MM-DD"
                                           type="text"
                                           id="c-time_start"
                                           name="row[time_start]"
                                           data-use-current="true"
                                           value="{:date('Y-m-d')}"
                                           placeholder="请选择时间">
<!--                                    <input id="c-time_start"  class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[time_start]" type="text" value="{:date('Y-m-d')}">-->
                                </div>
                            </div>
   
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">查询时间终点:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input class="form-control datetimepicker"
                                           data-date-format="YYYY-MM-DD"
                                           type="text"
                                           id="c-time_end"
                                           name="row[time_end]"
                                           data-use-current="true"
                                           value="{:date('Y-m-d')}"
                                           placeholder="请选择时间">
<!--                                    <input id="c-time_end" class="form-control datetimepicker"  data-date-format="YYYY-MM-DD" data-use-current="true" name="row[time_end]" type="text" value="{:date('Y-m-d')}">-->
                                </div>
                            </div>
                            <div class="form-group" style="margin-left: 40%;color: #FFFFFF">
                                <button type="button"  style="background-color: #2087fe;border: none;width: 80px;height: 40px;border-radius: 6px;font-size: 16px;box-shadow: 0 0 5px grey"  id="submit_buttons">查询</button>
                            </div>
                            

                            
                        </form>
                        
                        
                        <table id='search_money' style="display">
                            <thead>
                            <tr>
                                <th style="width:150px">团队总用户</th>
                                <th style="width:150px">团队新增用户</th>
                                <th style="width:150px">团队总充值</th>
                                <th style="width:150px;">团队新增充值</th>
                                <th style="width:150px;">团队总佣金</th>
                                <th style="width:150px;">周期总佣金</th>
                            </tr>
                            </thead>
                            <tbody>
                                
                                <tr style="height:40px">
                                    
                                    <td id='total_invite_num'></td>
                                    <td id='today_invite_num'></td>
                                    <td id='total_diff_money'></td>
                                    <td id='today_diff_money'></td>
                                    <td id='total_income'></td>
                                    <td id='today_income_money'></td>

                                </tr>
                            </tbody>
                        </table>
                        
        <!-- 加载JS脚本 -->
                        <script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>

                        <script>
                            $(document).ready(function(){
                                $.post("/OjRoBPiuFw.php/money_log/get_inc_defaults?id={$id}",{},
                                    function(data,status){
                                        //  console.log(data);
                                        var date = ['day'];

                                        var name = ['总量'];
                                        if(data.code=='1'){
                                            var list = '';
                                            var arr = data.data;
                                            for(var ii=0;ii<date.length;ii++){
                                                list +='<tr style="height:40px">';
                                                list += '<td>'+ name[ii] +'</td>';
                                                //  console.log(date[ii]);
                                                var item_name= date[ii];
                                                //  console.log(arr[item_name]);
                                                //  console.log(arr[item_name][2]);
                                                for(var i=0;i<6;i++){
                                                    // list += '<td>'+ arr.[date[ii]][i] +'</td>';
                                                    list += '<td>'+arr[item_name][i]+'</td>';
                                                }
                                                list += '</tr>';
                                            }
                                            $('#default_nums').html(list);
                                        }
                                        //alert("数据: \n" + data + "\n状态: " + status);
                                    });
                                $("#submit_buttons").click(function(){
                                    $.post("/OjRoBPiuFw.php/money_log/get_inc_money?id={$id}",{
                                            time_start:$("#c-time_start").val(),
                                            time_end:$("#c-time_end").val()
                                        },
                                        function(data,status){
                                            if(data.code=='1'){
                                                var arr = data.data;
                                                $('#total_invite_num').html(arr[0]);
                                                $('#today_invite_num').html(arr[1]);
                                                $('#total_diff_money').html(arr[2]);
                                                $('#today_diff_money').html(arr[3]);
                                                $('#total_income').html(arr[4]);
                                                $('#today_income_money').html(arr[5]);
                                            }
                                            return;
                                        });
                                });

                            });
                        </script>
                        
                        
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
