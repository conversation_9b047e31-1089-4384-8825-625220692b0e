<?php

namespace app\partner\controller\user;

use app\common\controller\PartnerBase;
use think\Session;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;

/**
 * 系统消息管理
 *
 * @icon fa fa-circle-o
 */
class SysNotice extends PartnerBase
{

    /**
     * SysNotice模型对象
     * @var \app\admin\model\SysNotice
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\partner\model\SysNotice;
        $this->view->assign("msgStatusList", $this->model->getMsgStatusList());
        $user_id = Session::get('admin')['id'];
        $this->view->assign("user_id", $user_id);
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','user_id','title','content','msg_status','createtime']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['nickname']);
            }
            
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        
        return $this->view->fetch();
    }
    
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        
        $user_id = $params['user_id'];
        if($user_id==0 || in_array('0',explode(',',$user_id))){
            $user_id = Session::get('admin')['id'];
            $list = [['id'=>$user_id,'nickname'=>'']];
            $this->select_user([['id'=>$user_id,'nickname'=>'']],$list);
            
            $ids = [];
            foreach($list as $y){
                $ids[] = $y['id'];
            }
            
        }else{
            $ids = explode(',',$user_id);
        }
        $add = [];
        $time = time();
        foreach ($ids as $uid){
            $add[] = [
                    'user_id'=>$uid,
                    'title'=>$params['title'],
                    'content'=>$params['content'],
                    'msg_status'=>'0',
                    'createtime'=>$time,
                    'have_voice'=>1,
                ];
        }
        Db::name('sys_notice')->insertAll($add);
        Db::name('user')->where('id','in',$ids)->inc('had_sysmsg_num',1);

        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }
    
    
    
    
    public function select_user($li,&$arr){
        foreach($li as $y){
            $f = Db::name('user')->where('p_user',$y['id'])->field('id,nickname')->select();
            if(!empty($f)){
                $arr = array_merge($arr,$f);
                $this->select_user($f,$arr);
            }else{
                return $arr;
            }
        }
    }
}
