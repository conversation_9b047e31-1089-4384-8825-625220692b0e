<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id}">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}" readonly=" readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" class="form-control" size="50" name="row[avatar]" type="text" value="{$row.avatar|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('True_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-true_name" class="form-control" name="row[true_name]" type="text" value="{$row.true_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('True_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-true_code" class="form-control" name="row[true_code]" type="text" value="{$row.true_code|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Auth_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-auth_image" class="form-control" size="50" name="row[auth_image]" type="text" value="{$row.auth_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-auth_image" class="btn btn-danger faupload" data-input-id="c-auth_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-auth_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-auth_image" class="btn btn-primary fachoose" data-input-id="c-auth_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-auth_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-auth_image"></ul>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Img_auth')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-img_auth" class="form-control selectpicker" name="row[img_auth]">
                {foreach name="imgAuthList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.img_auth"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Auth_file')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-auth_file" class="form-control" size="50" name="row[auth_file]" type="text" value="{$row.auth_file|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-auth_file" class="btn btn-danger faupload" data-input-id="c-auth_file" data-multiple="false" data-preview-id="p-auth_file"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-auth_file" class="btn btn-primary fachoose" data-input-id="c-auth_file" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-auth_file"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-auth_file"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Video_auth')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-video_auth" class="form-control selectpicker" name="row[video_auth]">
                {foreach name="videoAuthList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.video_auth"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Video_failure')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-video_failure" class="form-control" name="row[video_failure]" type="text" value="{$row.video_failure|htmlentities}">
        </div>
    </div>
    
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('similar_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-similar_rate" data-rule="required" class="form-control" name="row[similar_rate]" type="number" value="{$row.similar_rate|htmlentities}">
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>