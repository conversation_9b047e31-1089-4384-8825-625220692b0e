<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f83dd8c3-3b14-4900-a2a4-fbaae7e11a12" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/deployment.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/jsLinters/jshint.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/jsLinters/jshint.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/modules.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/php.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/php.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/phpunit.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/phpunit.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/tangxweb.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/webServers.xml" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/addons/cos/library/Guzzle/guzzle-services/composer.json" />
      <option value="$PROJECT_DIR$/extend/GatewayWorker/composer.json" />
      <option value="$PROJECT_DIR$/thinkphp/composer.json" />
      <option value="$PROJECT_DIR$/public/assets/libs/eonasdan-bootstrap-datetimepicker/composer.json" />
      <option value="$PROJECT_DIR$/public/assets/libs/jstree/composer.json" />
      <option value="$PROJECT_DIR$/public/assets/libs/bootstrap-table/composer.json" />
      <option value="$PROJECT_DIR$/addons/cos/library/Guzzle/command/composer.json" />
      <option value="$PROJECT_DIR$/addons/cos/library/Guzzle/uri-template/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/tencentcloud/tencentcloud-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-captcha" />
      <path value="$PROJECT_DIR$/vendor/topthink/framework" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-installer" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-queue" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/vendor/txthinking/mailer" />
      <path value="$PROJECT_DIR$/vendor/yansongda/supports" />
      <path value="$PROJECT_DIR$/vendor/yansongda/pay" />
      <path value="$PROJECT_DIR$/vendor/adbario/php-dot-notation" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/credentials" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/cloudauth-20190307" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea-oss-utils" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea-oss-sdk" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea-xml" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea-utils" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/easywechat-composer/easywechat-composer" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/dypnsapi-20170525" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/darabonba-openapi" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/gateway-spi" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/endpoint-util" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/openplatform-20191219" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/openapi-util" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea-fileform" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea" />
      <path value="$PROJECT_DIR$/vendor/lizhichao/one-sm" />
      <path value="$PROJECT_DIR$/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/karsonzhang/fastadmin-addons" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/vendor/pimple/pimple" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/myclabs/php-enum" />
      <path value="$PROJECT_DIR$/vendor/nelexa/zip" />
      <path value="$PROJECT_DIR$/vendor/overtrue/pinyin" />
      <path value="$PROJECT_DIR$/vendor/overtrue/wechat" />
      <path value="$PROJECT_DIR$/vendor/overtrue/socialite" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/extend/GatewayWorker/vendor/workerman/workerman" />
      <path value="$PROJECT_DIR$/extend/GatewayWorker/vendor/workerman/gateway-worker" />
      <path value="$PROJECT_DIR$/extend/GatewayWorker/vendor/composer" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2m2bPXE6Zd0KCQyQSwyOWtQ2L2L" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-php-predefined-ba97393d7c68-b4dcf6bb9de9-com.jetbrains.php.sharedIndexes-PS-233.13135.108" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f83dd8c3-3b14-4900-a2a4-fbaae7e11a12" name="更改" comment="" />
      <created>1726277595505</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1726277595505</updated>
      <workItem from="1726277597283" duration="116000" />
      <workItem from="1726277721891" duration="4213000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>