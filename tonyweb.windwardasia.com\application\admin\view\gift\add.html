<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
<!--<div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('类型')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            {:build_radios('row[kind]', ['1'=>'普通礼物', '2'=>'虚拟礼物', '3'=>'真实礼物'],'1')}-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('礼物名称（简体）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text">
        </div>
    </div>
   <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('礼物名称（韩文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ko_name" class="form-control" name="row[ko_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('礼物名称（日文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ja_name" class="form-control" name="row[ja_name]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('礼物名称（繁体）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hk_name" class="form-control" name="row[hk_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('礼物名称（英文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-en_name" class="form-control" name="row[en_name]" type="text">
        </div>
    </div>
     <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('礼物名称（德文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-de_name" class="form-control" name="row[de_name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('礼物名称（法文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fr_name" class="form-control" name="row[fr_name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" class="form-control" size="50" name="row[image]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('File')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-file" class="form-control" size="50" name="row[file]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-file" class="btn btn-danger faupload" data-input-id="c-file" data-multiple="false" data-preview-id="p-file"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-file" class="btn btn-primary fachoose" data-input-id="c-file" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-file"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-file"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-price" class="form-control" name="row[price]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('On_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-on_switch" class="form-control selectpicker" name="row[on_switch]">
                {foreach name="onSwitchList" item="vo"}
                    <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="0">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
