<?php

return [
    [
        'name' => 'appId',
        'title' => 'AppID',
        'type' => 'string',
        'content' => [],
        'value' => '1',
        'rule' => 'required',
        'msg' => '',
        'tip' => '请前往腾讯控制台 > 访问管理 > API密钥',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'secretId',
        'title' => 'SecretId',
        'type' => 'string',
        'content' => [],
        'value' => '2',
        'rule' => 'required',
        'msg' => '',
        'tip' => '请前往腾讯控制台 > 访问管理 > API密钥',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'secretKey',
        'title' => 'SecretKey',
        'type' => 'string',
        'content' => [],
        'value' => '3',
        'rule' => 'required',
        'msg' => '',
        'tip' => '请前往腾讯控制台 > 访问管理 > API密钥',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'bucket',
        'title' => '存储桶名称',
        'type' => 'string',
        'content' => [],
        'value' => 'wqw',
        'rule' => 'required;bucket',
        'msg' => '',
        'tip' => '存储空间名称',
        'ok' => '',
        'extend' => 'data-rule-bucket="[/^[0-9a-z_\\-]{3,63}$/, \'请输入正确的存储桶名称\']"',
    ],
    [
        'name' => 'region',
        'title' => '地域名称',
        'type' => 'string',
        'content' => [],
        'value' => '1',
        'rule' => 'required',
        'msg' => '',
        'tip' => '请输入地域简称,请注意使用英文',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'uploadmode',
        'title' => '上传模式',
        'type' => 'select',
        'content' => [
            'client' => '客户端直传(速度快,无备份)',
            'server' => '服务器中转(占用服务器带宽,有备份)',
        ],
        'value' => 'server',
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'serverbackup',
        'title' => '服务器中转模式备份',
        'type' => 'radio',
        'content' => [
            1 => '备份(附件管理将产生2条记录)',
            0 => '不备份',
        ],
        'value' => '0',
        'rule' => '',
        'msg' => '',
        'tip' => '服务器中转模式下是否备份文件',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'uploadurl',
        'title' => '上传接口地址',
        'type' => 'string',
        'content' => [],
        'value' => 'http://mixunoud.com',
        'rule' => 'required;uploadurl',
        'msg' => '',
        'tip' => '请输入你的上传接口地址',
        'ok' => '',
        'extend' => 'data-rule-uploadurl="[/^http(s)?:\\/\\/.*$/, \'必需以http(s)://开头\']"',
    ],
    [
        'name' => 'cdnurl',
        'title' => 'CDN地址',
        'type' => 'string',
        'content' => [],
        'value' => 'https://mixunnook.com',
        'rule' => 'required;cdnurl',
        'msg' => '',
        'tip' => '请配置你的CDN地址或在存储桶基础配置中获取',
        'ok' => '',
        'extend' => 'data-rule-cdnurl="[/^http(s)?:\\/\\/.*$/, \'必需以http(s)://开头\']"',
    ],
    [
        'name' => 'savekey',
        'title' => '保存文件名',
        'type' => 'string',
        'content' => [],
        'value' => '/uploads/{year}{mon}{day}/{filemd5}{.suffix}',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'expire',
        'title' => '上传有效时长',
        'type' => 'string',
        'content' => [],
        'value' => '600',
        'rule' => 'required',
        'msg' => '',
        'tip' => '用户停留页面上传有效时长，单位秒',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'maxsize',
        'title' => '最大可上传',
        'type' => 'string',
        'content' => [],
        'value' => '100M',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'mimetype',
        'title' => '可上传后缀格式',
        'type' => 'string',
        'content' => [],
        'value' => 'jpg,png,bmp,jpeg,webp,gif,zip,rar,xls,xlsx,mp4,mov,svga',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'multiple',
        'title' => '多文件上传',
        'type' => 'bool',
        'content' => [],
        'value' => '0',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'thumbstyle',
        'title' => '缩略图样式',
        'type' => 'string',
        'content' => [],
        'value' => '',
        'rule' => '',
        'msg' => '',
        'tip' => '用于后台列表缩略图样式，可使用：?imageMogr2/thumbnail/120x90',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'chunking',
        'title' => '分片上传',
        'type' => 'radio',
        'content' => [
            1 => '开启',
            0 => '关闭',
        ],
        'value' => '0',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'chunksize',
        'title' => '分片大小',
        'type' => 'number',
        'content' => [],
        'value' => '4194304',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'syncdelete',
        'title' => '附件删除时是否同步删除文件',
        'type' => 'bool',
        'content' => [],
        'value' => '1',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'apiupload',
        'title' => 'API接口使用云存储',
        'type' => 'bool',
        'content' => [],
        'value' => '1',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'noneedlogin',
        'title' => '免登录上传',
        'type' => 'checkbox',
        'content' => [
            'api' => 'API',
            'index' => '前台',
            'admin' => '后台',
        ],
        'value' => 'api',
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
];
