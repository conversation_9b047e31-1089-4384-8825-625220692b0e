<?php

namespace app\admin\model;

use think\Model;


class Version extends Model
{

    

    

    // 表名
    protected $name = 'version';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_status_text',
        'status_text'
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
        });
    }

    
     public function getPlatformList(){
        return ['1' => __('platform 1'), '2' => __('platform 2'),'3' => __('platform 3'),
        '4' => __('platform 4'),'5' => __('platform 5'),'6' => __('platform 6')];
    }
    
    public function getTypeStatusList(){
        return ['ios' => __('Type_status ios'), 'android' => __('Type_status android')];
    }
    
    public function getStatusList(){
        return ['1'=> __('Status 1'), '2'=> __('Status 2'),'3'=> __('Status 3')];
    }

    public function getOnSwitchList(){
        return ['1' => __('On_switch 1'), '0' => __('On_switch 0')];
    }


    public function getTypeStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type_status']) ? $data['type_status'] : '');
        $list = $this->getTypeStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    public function getStatusTextAttr($value, $data){
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getOnSwitchTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['on_switch']) ? $data['on_switch'] : '');
        $list = $this->getOnSwitchList();
        return isset($list[$value]) ? $list[$value] : '';
    }




}
