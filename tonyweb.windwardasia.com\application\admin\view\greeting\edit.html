<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('问候语（简体）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hot_word" class="form-control" name="row[hot_word]" type="text" value="{$row.hot_word|htmlentities}">
        </div>
    </div>
   <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('问候语（繁体）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hk_hot_word" class="form-control" name="row[hk_hot_word]" type="text" value="{$row.hk_hot_word|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('问候语（韩文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ko_hot_word" class="form-control" name="row[ko_hot_word]" type="text" value="{$row.ko_hot_word|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('问候语（日文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ja_hot_word" class="form-control" name="row[ja_hot_word]" type="text" value="{$row.ja_hot_word|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('问候语（英文）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-en_hot_word" class="form-control" name="row[en_hot_word]" type="text" value="{$row.en_hot_word|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('问候语（德语）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-de_hot_word" class="form-control" name="row[de_hot_word]" type="text" value="{$row.de_hot_word|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('问候语（法语）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fr_hot_word" class="form-control" name="row[fr_hot_word]" type="text" value="{$row.fr_hot_word|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('On_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-on_switch" class="form-control selectpicker" name="row[on_switch]">
                {foreach name="onSwitchList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.on_switch"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
