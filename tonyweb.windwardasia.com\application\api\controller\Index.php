<?php

namespace app\api\controller;

header('Access-Control-Allow-Origin:*');

use app\common\controller\Api;
use app\api\library\Sample;
use app\common\library\Redis;
use app\pay\common\Lxpay;
use fast\Http;
use think\Env;
use think\Request;
use think\Db;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Cloudauth\*********\Cloudauth;
use AlibabaCloud\SDK\Cloudauth\*********\Models\DescribeFaceVerifyRequest;
use AlibabaCloud\SDK\Dypnsapi\*********\Dypnsapi;
use AlibabaCloud\SDK\Dypnsapi\*********\Models\GetMobileRequest;
use think\Validate;

class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function welopay()
    {
        $params = input();
        $rule = [
            'order_no' => 'require|max:50',
            'amount' => 'require|float',
            'return_url' => 'require|url',
            'notify_url' => 'require|url',
            'cancel_url' => 'require|url',
            'shopper_id' => 'require',
            'shopper_email' => 'require|email',
            'shopper_phone' => 'require',
            'billing_state' => 'require',
            'billing_city' => 'require',
            'billing_address' => 'require',
            'billing_postal_code' => 'require',
        ];

        $validate = new Validate($rule);
        if (!$validate->check($params)) {
            $this->error('参数错误: ' . $validate->getError());
        }

        $params['merchant_id'] = Env::get('welopay.merchant_id');
        $params['account_id'] = Env::get('welopay.account_id');
        $params['payment_method'] = 'PAYPAL';
        $params['currency'] = 'USD';
        $params['items'] = '虚拟金币#,#001#,#' . $params['amount'] . '#,#1#,#0';
        $sign_key = Env::get('welopay.key');
        $encryption_data = hash('sha256', $params['merchant_id'] . $params['account_id'] . $params['order_no'] . $params['currency'] . $params['amount'] . $params['return_url'] . $sign_key);
        $data = array_merge($params, ['encryption_data' => $encryption_data]);

        if ($data) {
            $this->success('ok', Env::get('url.uri') . 'welopay/pay/pay?' . http_build_query($data, true));
        } else {
            $this->error('请求失败');
        }
    }
    public function welopay_notify()
    {
        $params = input();
        $required_params = [
            'merchant_id', 'account_id', 'transaction_id', 'order_no', 'amount',
            'currency', 'order_status', 'result_info', 'encryption_data'
        ];

        foreach ($required_params as $param) {
            if (!isset($params[$param])) {
                $this->error("缺少参数: $param");
            }
        }

        if ($params['order_status'] == '1') {
            $data = [
                'out_order_no' => $params['order_no'],
                'amount' => $params['amount'],
                'order_no' => $params['transaction_id']
            ];
            $url = Env::get('url.url') . 'money/notify_welo_pay?';
            Http::post($url, $data);
            return 'SUCCESS';
        } else {
            return 'FAIL';
        }
    }

    public function lx_pay(Request $request)
    {
        $result = '';
        try {
            $param = $this->request->param();
            $validate = new Validate([
                ['order_sn', 'require', '订单编号错误'],
                ['money', 'require', '金额错误'],
                ['notify_url', 'require', '回调地址错误'],
                ['desc', 'require', '备注错误'],
            ]);
            if (!$validate->check($param)) {
                $this->error($validate->getError());
            }

            $pay = new Lxpay();
            Db::startTrans();
            $result = $pay->payment($param['order_sn'], $param['money'], $param['notify_url'], $param['desc']);
        } catch (\think\Exception $exception) {
            $this->error($exception->getMessage());
        }
        $this->success('ok', compact('result'));
    }

    public function notify()
    {
        $param = $this->request->param();
        $pay = new Lxpay();
        $data = $pay->decrypt($param);
        if (!$data) return 'FAIL';

        $notify_data = [
            'out_order_no' => $data['Result']['MerchantOrderNo'],
            'amount' => $data['Result']['Amt'],
            'order_no' => $data['Result']['TradeNo']
        ];
        $url = Env::get('url.url') . 'money/notify_lx_pay?';
        $result = Http::post($url, $notify_data);

        return $result ? 'SUCCESS' : 'FAIL';
    }

    public function setHotName()
    {
        $Redis = (new Redis())->handler;
        $prefix = Env::get('redis.redis_prefix');
        $name = Db::name('hot_name')->column('name');
        $Redis->del($prefix . 'hot_nickname');
        foreach ($name as $v) {
            $Redis->sAdd($prefix . 'hot_nickname', $v);
        }
    }

    public function indexs()
    {
        $array1 = [];
        $array2 = ['b', 'd', 'f'];
        $array2 = array_pad($array2, count($array1), null);
        $mergedArray = [];
        for ($i = 0, $max = count($array1); $i < $max; $i++) {
            if ($i < count($array1)) {
                $mergedArray[] = $array1[$i];
            }
            if ($i < count($array2)) {
                $mergedArray[] = $array2[$i];
            }
        }
    }

    public function defaultOpen()
    {
        $this->success('', \config('site.default_open'));
    }

    public function get_sql()
    {
    }

    public function insertTest()
    {
        $num = mt_rand(10000, 99999);
        $sameUser = Db::name('user')->where('id', '=', '38197')->find();
        if ($sameUser) {
            $this->insertTest();
        } else {
            $data = ['id' => $num];
            Db::name('user')->insert($data);
        }
    }

    public function get_mobile(Request $req)
    {
        $AccessToken = $req->param('AccessToken');
        $response = Sample::main($AccessToken);

        if ($response->body->code != 'OK') {
            $this->error('错误码:' . $response->body->code . '--' . $response->body->message);
        }
        $this->success('获取成功', $response->body->getMobileResultDTO->mobile);
    }

    public function getAliMobile()
    {
        $assess_token = $this->request->param('access_token');
        if (empty($assess_token)) {
            $this->error('传输异常');
        }

        $client = self::client();
        $getMobileRequest = new GetMobileRequest(["accessToken" => $assess_token]);
        $response = $client->getMobile($getMobileRequest);

        if ($response->body->code != 'OK') {
            $this->error('错误码:' . $response->body->code . '--' . $response->body->message);
        }
        $this->success('获取成功', $response->body->getMobileResultDTO->mobile);
    }

    public static function client()
    {
        $config = new \Darabonba\OpenApi\Models\Config([
            "accessKeyId" => "LTAI5tFMLiJQbS7SNFa7Hm4r",
            "accessKeySecret" => "******************************",
        ]);
        $config->endpoint = "dypnsapi.aliyuncs.com";
        return new Dypnsapi($config);
    }

    public function getMobileAli($access_token)
    {
        $client = self::client();
        $getMobileRequest = new GetMobileRequest(["accessToken" => $access_token]);
        $response = $client->getMobile($getMobileRequest);

        if ($response->body->code != 'OK') {
            $this->error('错误码:' . $response->body->code . '--' . $response->body->message);
        }
        return $response->body->getMobileResultDTO->mobile;
    }

    public function pay(Request $req)
    {
        $order_sn = $req->param('order_sn/s', '');
        $pay_type = $req->param('pay_type/s', '');
        $pay_proof = $req->param('pay_proof/s', '');

        if (!in_array($pay_type, ['alipay', 'wechat', 'yue', 'pg'])) {
            $this->error('请选择支付方式');
        }

        $type = substr($order_sn, 0, 2);
        $info = null;
        $money = 0;
        $text = '';

        switch ($type) {
            case 'VI':
                $info = Db::name('vip_order')->where('order_sn', $order_sn)->find();
                if (!$info) {
                    $this->error('订单不存在');
                }
                if ($info['status'] == '2') {
                    $this->error('该订单已过期');
                }
                if ($info['status'] == '1') {
                    $this->error('该订单已支付');
                }
                $money = $info['price'];
                $text = '购买VIP';
                break;

            case 'RE':
                $info = Db::name('recharge_order')->where('order_sn', $order_sn)->find();
                if (!$info) {
                    $this->error('订单不存在');
                }
                if ($info['status'] == '2') {
                    $this->error('该订单已过期');
                }
                if ($info['status'] == '1') {
                    $this->error('该订单已支付');
                }
                $money = $info['amount'];
                $text = ($pay_type == 'yue') ? '佣金兑换钻石' : '充值钻石';
                break;
        }

        $OBJ = new \addons\epay\controller\Api;

        switch ($pay_type) {
            case 'alipay':
                $str = $OBJ->submit_other_api($order_sn, 'alipay', $money, $text);
                $this->success('订单创建成功', ['price' => $money, 'pay' => $str, 'wechat_pay' => (object)[]]);
                break;
            case 'wechat':
                $str = $OBJ->submit_other_api($order_sn, 'wechat', $money, $text);
                $this->success('订单创建成功', ['price' => $money, 'pay' => '', 'wechat_pay' => $str]);
                break;
            case 'yue':
                $my_money = Db::name('user')->where('id', $info['user_id'])->value('money');
                if ($my_money < $money) {
                    $this->error('余额不足');
                }
                \app\common\model\User::money(-$money, $info['user_id'], $text);
                if ($type == 'VI') {
                    $OBJ->vip_buy_order($order_sn, 'yue', time(), $money);
                } else {
                    $OBJ->recharge_order($order_sn, 'yue', time(), $money);
                }
                $this->success('支付成功', (object)[]);
                break;
            case 'pg':
                if ($pay_proof == '') {
                    $this->error('无票据信息');
                }

                $res = $this->validate_applepay_panduan($pay_proof, true);
                if (intval($res['status']) == 0) {
                    $ios_bundle_id = config('site.ios_bundle_id');
                    if ($ios_bundle_id != $res['receipt']['bundle_id']) {
                        $this->error('应用信息错误');
                    }
                    $good_code = $res['receipt']['in_app'][0]['product_id'];
                    if ($good_code != $info['good_code']) {
                        $this->error('商品不一致');
                    }

                    $transaction_id = $res['receipt']['in_app'][0]['transaction_id'];
                    switch ($type) {
                        case 'VI':
                            $if = Db::name('vip_order')->where('transaction_id', $transaction_id)->count();
                            if ($if) {
                                $this->error('票据已使用');
                            }
                            break;
                        case 'RE':
                            $if = Db::name('recharge_order')->where('transaction_id', $transaction_id)->count();
                            if ($if) {
                                $this->error('票据已使用');
                            }
                            break;
                    }

                    if ($type == 'VI') {
                        $OBJ->vip_buy_order($order_sn, 'pg', $transaction_id, $money);
                    } else {
                        $OBJ->recharge_order($order_sn, '3', $transaction_id, $money);
                    }
                    $this->success('支付成功', (object)[]);
                } else {
                    $this->error('票据验证失败', (object)[]);
                }
                break;
            default:
                break;

        }

    }

    public function validate_applepay_panduan($receipt_data, $sandbox)
    {
        $endpoint = $sandbox ? 'https://buy.itunes.apple.com/verifyReceipt' : 'https://sandbox.itunes.apple.com/verifyReceipt';
        $postData = json_encode(['receipt-data' => $receipt_data]);

        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);

        $response = curl_exec($ch);
        $errno = curl_errno($ch);
        curl_close($ch);

        if ($errno != 0) {
            return false;
        }

        $data = json_decode($response, true);
        if (!is_array($data) || !isset($data['status']) || $data['status'] != 0) {
            return false;
        }

        return $data;
    }




    public function upload_photo(Request $req)
    {
        $user_id = $req->param('user_id/s', '');
        $imgs = $req->param('imgs/s', '');
        $img_arr = explode(',', $imgs);
        $UPOBj = new \addons\qiniu\controller\Upl();
        $add = [];

        foreach ($img_arr as $y) {
            $add[] = [
                'user_id' => $user_id,
                'file_type' => '1',
                'image' => $y,
                'type_status' => '1',
                'coin' => 0,
                'status' => '2'
            ];
            $UPOBj->upload_excel('.' . $y);
        }

        Db::name('photo')->insertAll($add);
    }

    public function upload_head(Request $req)
    {
        $user_id = $req->param('user_id/s', '');
        $head = $req->param('head/s', '');
        $imgs = $req->param('imgs/s', '');
        $UPOBj = new \addons\qiniu\controller\Upl();

        if ($imgs) {
            $img_arr = explode(',', $imgs);
            $add = [];
            foreach ($img_arr as $y) {
                $add[] = [
                    'user_id' => $user_id,
                    'file_type' => '1',
                    'image' => $y,
                    'type_status' => '1',
                    'coin' => 0,
                    'status' => '2'
                ];
                $UPOBj->upload_excel('.' . $y);
            }
            Db::name('photo')->insertAll($add);
        }

        $UPOBj->upload_excel('.' . $head);
        Db::name('user')->where('id', $user_id)->update(['avatar' => $head]);
    }

    public function imgs_upload(Request $req)
    {
        $imgs = $req->param('imgs/s', '');
        $img_arr = explode(',', $imgs);
        $UPOBj = new \addons\qiniu\controller\Upl();
        $s = [];

        foreach ($img_arr as $y) {
            $s = $UPOBj->upload_excel('.' . $y);
        }
        $this->success('', $s);
    }

    public function face_verify(Request $req)
    {
        $user_id = $req->param('user_id/d', 0);
        $certifyId = $req->param('certifyId/s', '');
        if (!$user_id || !$certifyId) {
            $this->error('参数错误');
        }

        $client = $this->createClient();
        $describeFaceVerifyRequest = new DescribeFaceVerifyRequest(["certifyId" => $certifyId]);
        $response = $client->describeFaceVerify($describeFaceVerifyRequest);
        $result = $response->body->resultObject;

        if ($result->passed == 'T') {
            $this->success('验证成功', $result);
        } else {
            $this->error('验证失败', $result);
        }
    }

    public function createClient()
    {
        $config = new Config([
            "accessKeyId" => "LTAI5tFMLiJQbS7SNFa7Hm4r",
            "accessKeySecret" => "******************************",
        ]);
        $config->endpoint = "cloudauth.cn-shanghai.aliyuncs.com";
        return new Cloudauth($config);
    }
}
