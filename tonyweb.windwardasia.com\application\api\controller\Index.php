<?php

namespace app\api\controller;

header('Access-Control-Allow-Origin:*');

use app\common\controller\Api;
use app\api\library\Sample;

use app\common\library\Log;
use app\common\library\Redis;
use app\pay\common\Lxpay;
use fast\Http;
use think\Env;
use think\Request;
use think\Db;

// use app\common\library\Redis;
// use think\Env;
//use AlibabaCloud\SDK\Dypnsapi\*********\Dypnsapi;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;

//use AlibabaCloud\Tea\Utils\Utils;

use Darabonba\OpenApi\Models\Config;

//use AlibabaCloud\SDK\Dypnsapi\*********\Models\GetMobileRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;


use AlibabaCloud\SDK\Cloudauth\*********\Cloudauth;
use AlibabaCloud\SDK\Cloudauth\*********\Models\DescribeFaceVerifyRequest;
use AlibabaCloud\SDK\Cloudauth\*********\Models\DescribeFaceVerifyResponse;
use AlibabaCloud\SDK\Cloudauth\*********\Models\InitFaceVerifyRequest;
use AlibabaCloud\SDK\Cloudauth\*********\Models\InitFaceVerifyResponse;
use AlibabaCloud\SDK\Dypnsapi\*********\Dypnsapi;
use AlibabaCloud\SDK\Dypnsapi\*********\Models\GetMobileRequest;
use AlibabaCloud\Tea\Utils\Utils;
use think\Validate;
use function EasyWeChat\Kernel\Support\str_random;


/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    

    public function welopay()
    {
        // 获取HTTP请求参数
        $params = input();
        $rule = [
            'order_no'     => 'require|max:50',
            'amount'       => 'require|float',
            'return_url'   => 'require|url',
            'notify_url'   => 'require|url',
            'cancel_url'   => 'require|url',
            'shopper_id'   => 'require',
            'shopper_email'=> 'require|email',
            'shopper_phone'=> 'require',
            'billing_state'=> 'require',
            'billing_city'=> 'require',
            'billing_address'=> 'require',
            'billing_postal_code'=> 'require',
        ];
        // $this->success('',$params);
        $validate = new Validate($rule);
        if (!$validate->check($params)) {
            $this->error('参数错误: ' . $validate->getError());
        }
        // 签名加密
        $params['merchant_id'] = Env::get('welopay.merchant_id');
        $params['account_id'] = Env::get('welopay.account_id');
        $params['payment_method'] = 'PAYPAL';
        $params['currency'] = 'USD';
        $params['items'] = '虚拟金币#,#001#,#'.$params['amount'].'#,#1#,#0';
        $sign_key = Env::get('welopay.key'); // 签名密钥
        $encryption_data = hash('sha256', $params['merchant_id'].$params['account_id'].$params['order_no'].$params['currency'].$params['amount'].$params['return_url'].$sign_key); // 加密数据
        // 创建请求数据
        $data = array_merge($params, ['encryption_data' => $encryption_data]);
        // 处理响应
        if ($data) {
            $this->success('ok',Env::get('url.uri').'welopay/pay/pay?'.http_build_query($data,true));
            // $this->success('请求成功',['data' => $data]);
        } else {
            $this->error('请求失败');
        }
    }
    
     /**
     * 支付回调
     * @ApiMethod (POST)
     * @ApiRoute  (/api/callback/notify)
     */
    public function welopay_notify(Request $request)
    {
        $params = input();
        // adlog('回调',$params);
        // 定义商户的签名密钥
        $sign_key = Env::get('welopay.key'); // 签名密钥
        // 验证请求参数是否完整
        $required_params = [
            'merchant_id', 'account_id', 'transaction_id', 'order_no', 'amount',
            'currency', 'order_status', 'result_info', 'encryption_data'
        ];
        foreach ($required_params as $param) {
            if (!isset($params[$param])) {
                $this->error("缺少参数: $param");
            }
        }
  
        // 处理订单状态
        if ($params['order_status'] == '1') {
            $out_order_no = $params['order_no'];
            $amount = $params['amount'];
            $order_no = $params['transaction_id'];
            $data      = compact('out_order_no', 'amount', 'order_no');
            $url       = Env::get('url.url') . 'money/notify_welo_pay?';
            $result = Http::post($url,$data);
            // 订单成功处理逻辑
            return 'SUCCESS';
        } else {
            // 订单失败处理逻辑
              return 'FAIL';
        }
    }

    /**
     * 蓝新支付请求接口
     * @param Request $request
     * @return void
     */
    public function lx_pay(Request $request)
    {
        $param    = $this->request->param();
        $validate = new Validate([
            ['order_sn', 'require', '订单编号错误'],
            ['money', 'require', '金额错误'],
            ['notify_url', 'require', '回调地址错误'],
            ['desc', 'require', '备注错误'],
        ]);
        if (!$validate->check($param)) $this->error($validate->getError());
        $pay = new Lxpay();
        Db::startTrans();
        try {
            $result = $pay->payment($param['order_sn'], $param['money'], $param['notify_url'], $param['desc']);
            $this->success('ok', compact('result'));
        } catch (\think\Exception $exception) {
            $this->error($exception->getMessage());
        }
    }


    /**
     * 蓝新金流
     * 回调处理
     */
    public function notify()
    {
        $param = $this->request->param();
        $pay   = new Lxpay();
        $data  = $pay->decrypt($param);
        if (!$data) return 'FAIL';
        $out_order_no = $data['Result']['MerchantOrderNo'];
        $amount       = $data['Result']['Amt'];
        $order_no     = $data['Result']['TradeNo'];
        $data      = compact('out_order_no', 'amount', 'order_no');
//        $post_data = http_build_query($data);
        $url       = Env::get('url.url') . 'money/notify_lx_pay?';
        $result = Http::post($url,$data);
//        $result    = file_get_contents($url . $post_data);
        if ($result) {
            return 'SUCCESS';
        } else {
            return 'FAIL';
        }
    }

    /**
     * 设置虚拟名称
     * @return void
     * @throws \RedisException
     */
    public function setHotName()
    {
        $Redis  = (new Redis())->handler;
        $prefix = Env::get('redis.redis_prefix');
        $name   = Db::name('hot_name')->column('name');
        $Redis->del($prefix . 'hot_nickname');
        foreach ($name as $v) {
            $Redis->sAdd($prefix . 'hot_nickname', $v);
        }
    }


    /**
     * 重组数据
     * @return void
     */
    public function indexs()
    {
        $array1 = []; // 较长数组
        $array2 = ['b', 'd', 'f'];       // 较短数组
        // 用null填充array2以匹配array1的长度
        $array2 = array_pad($array2, count($array1), null);
        // 交替插入元素
        $mergedArray = [];
        for ($i = 0, $max = count($array1); $i < $max; $i++) {
            if ($i < count($array1)) {
                $mergedArray[] = $array1[$i];
            }
            if ($i < count($array2)) {
                $mergedArray[] = $array2[$i];
            }
        }
        var_dump($mergedArray);
        die;
    }


    public function defaultOpen()
    {
        $this->success('', \config('site.default_open'));
    }

    public function get_sql()
    {
        var_dump(phpinfo());
        die;

    }


    public function insertTest()
    {
        $num      = mt_rand(10000, 99999);
        $sameUser = Db::name('user')->where('id', '=', '38197')->find();
        if ($sameUser) {
            $this->insertTest();
        } else {
            $data = [
                'id' => $num
            ];
            Db::name('user')->insert($data);
        }
        var_dump(1);
        die;

    }

    public function get_mobile(Request $req)
    {
        $AccessToken = $req->param('AccessToken');
        // $path = __DIR__ . \DIRECTORY_SEPARATOR . '..' . \DIRECTORY_SEPARATOR . 'vendor' . \DIRECTORY_SEPARATOR . 'autoload.php';
        // dump($path);
        // if (file_exists($path)) {
        //     require_once $path;
        // }
        $response = Sample::main($AccessToken);
        var_dump($response);
        print_r($response->body);
        exit;
        if ($response->body->code != 'OK') {
            $this->error('错误码:' . $response->body->code . '--' . $response->body->message);
        }
        $this->success('获取成功', $response->body->getMobileResultDTO->mobile);
        //$this->success('',$response);
    }


    // 阿里云一键登录-获取手机号
    public function getAliMobile()
    {
        $assess_token = $this->request->param('access_token');
        if (empty($assess_token)) $this->error('传输异常');
        $client           = self::client();
        $getMobileRequest = new GetMobileRequest([
            "accessToken" => $assess_token
        ]);
        $response         = $client->getMobile($getMobileRequest);
        //print_r($response->body);exit;
        if ($response->body->code != 'OK') {
            $this->error('错误码:' . $response->body->code . '--' . $response->body->message);
        }
        $this->success('获取成功', $response->body->getMobileResultDTO->mobile);


        /*$runtime = new Utils\RuntimeOptions([]);
        $client->getMobileWithOptions($getMobileRequest, $runtime);*/


    }

    public static function client()
    {
        $config           = new \Darabonba\OpenApi\Models\Config([
            // 您的AccessKey ID。
            "accessKeyId"     => "LTAI5tFMLiJQbS7SNFa7Hm4r",
            // 您的AccessKey Secret。
            "accessKeySecret" => "******************************",
        ]);
        $config->endpoint = "dypnsapi.aliyuncs.com";
        return new Dypnsapi($config);
    }

    public function getMobileAli($access_token)
    {
        $client           = self::client();
        $getMobileRequest = new GetMobileRequest([
            "accessToken" => $access_token
        ]);
        $response         = $client->getMobile($getMobileRequest);
        //print_r($response->body);exit;
        if ($response->body->code != 'OK') {
            $this->error('错误码:' . $response->body->code . '--' . $response->body->message);
        }
        return $response->body->getMobileResultDTO->mobile;

        /*$runtime = new Utils\RuntimeOptions([]);
        $client->getMobileWithOptions($getMobileRequest, $runtime);*/


    }

    public function pay(Request $req)
    {
        $order_sn  = $req->param('order_sn/s', '');
        $pay_type  = $req->param('pay_type/s', '');
        $pay_proof = $req->param('pay_proof/s', '');
        // if($pay_type!='alipay' && $pay_type!='wechat' && $pay_type!='yue'){$this->error('请选择支付方式');}
        if (!in_array($pay_type, ['alipay', 'wechat', 'yue', 'pg'])) {
            $this->error('请选择支付方式');
        }
        //if(!preg_match('/^[(VI)|(RE)]\d{27,29}$/',$order_sn,$all)){var_dump($all);$this->error('参数格式错误');}
        $type = substr($order_sn, 0, 2);
        switch ($type) {
            case 'VI':
                $info = Db::name('vip_order')->where('order_sn', $order_sn)->find();
                if (!$info) {
                    $this->error('订单不存在');
                }
                if ($info['status'] == '2') {
                    $this->error('该订单已过期');
                }
                if ($info['status'] == '1') {
                    $this->error('该订单已支付');
                }
                $money = $info['price'];
                $text  = '购买VIP';

                break;

            case 'RE':
                $info = Db::name('recharge_order')->where('order_sn', $order_sn)->find();
                if (!$info) {
                    $this->error('订单不存在');
                }
                if ($info['status'] == '2') {
                    $this->error('该订单已过期');
                }
                if ($info['status'] == '1') {
                    $this->error('该订单已支付');
                }
                $money = $info['amount'];
                $text  = '充值钻石';
                if ($pay_type == 'yue') {
                    $text = '佣金兑换钻石';
                }
                break;
        }
        $OBJ = new \addons\epay\controller\Api;

        //调支付
        switch ($pay_type) {
            case 'alipay':
                $str = $OBJ->submit_other_api($order_sn, 'alipay', $money, $text);
                $this->success('订单创建成功', ['price' => $money, 'pay' => $str, 'wechat_pay' => (object)[]]);
                break;
            case 'wechat':
                $str = $OBJ->submit_other_api($order_sn, 'wechat', $money, $text);
                $this->success('订单创建成功', ['price' => $money, 'pay' => '', 'wechat_pay' => $str]);
                break;
            case 'yue':
                //扣费，写日志，订单回调
                $my_money = Db::name('user')->where('id', $info['user_id'])->value('money');
                if ($my_money < $money) {
                    $this->error('余额不足');
                }
                \app\common\model\User::money(-$money, $info['user_id'], $text);
                // \app\common\model\User::score($info['diamond'], $info['user_id'], $msg);
                if ($type == 'VI') {
                    $R4 = $OBJ->vip_buy_order($order_sn, 'yue', time(), $money);
                } else {
                    $R4 = $OBJ->recharge_order($order_sn, 'yue', time(), $money);
                }

                $this->success('支付成功', (object)[]);
                break;
            case 'pg'://是否提交了票据，是否已存在票据，票据商品和订单表商品是否一致，
                //是否提交了票据，
                if ($pay_proof == '') {
                    $this->error('无票据信息');
                }
                //票据商品和订单表商品是否一致
                $res = $this->validate_applepay_panduan($pay_proof, true); //false沙盒 true正式
                if (intval($res['status']) == 0) {  //验证成功
                    $ios_bundle_id = config('site.ios_bundle_id');
                    if ($ios_bundle_id != $res['receipt']['bundle_id']) {
                        $this->error('应用信息错误');
                    }
                    $good_code = $res['receipt']['in_app'][0]['product_id'];
                    if ($good_code != $info['good_code']) $this->error('商品不一致');
                    //查验支付流水号
                    $transaction_id = $res['receipt']['in_app'][0]['transaction_id'];
                    //是否已存在票据
                    switch ($type) {
                        case 'VI':
                            $if = Db::name('vip_order')->where('transaction_id', $transaction_id)->count();
                            if ($if) {
                                $this->error('票据已使用');
                            }
                            break;
                        case 'RE':
                            $if = Db::name('recharge_order')->where('transaction_id', $transaction_id)->count();
                            if ($if) {
                                $this->error('票据已使用');
                            }
                            break;
                    }
                    //所有的验证成功后
                    if ($type == 'VI') {
                        $R4 = $OBJ->vip_buy_order($order_sn, 'pg', $transaction_id, $money);
                    } else {
                        $R4 = $OBJ->recharge_order($order_sn, '3', $transaction_id, $money);
                    }

                    $this->success('支付成功', (object)[]);
                } else {  //验证失败
                    $this->error('票据验证失败', (object)[]);
                }
                break;
            default:
                break;

        }

        // $this->success('订单创建成功', ['price'=>$money,'pay'=>$str]);
    }


    // //IOS内购
    // public function applepay(){
    //     $info = $this->request->request();
    //     $user_id = $this->user_id;
    //     $goods_id = $this->request->request('goods_id');
    //     $receipt_data = $this->request->request('receipt_data') ?: 123;
    //     if (!$user_id || !$goods_id || !$receipt_data)   $this->ApiReturn(0, '缺少参数');
    //     if(strpos($goods_id,"com")!==false){
    //         $a=1;
    //         $goods = DB::name('goodst')->where('ios_id', $goods_id)->find();
    //     }else{
    //         $a=2;
    //         $goods = DB::name('goods')->where('ios_id', $goods_id)->find();
    //     }
    //     //adlog("goods",$goods);
    //     if (!$goods) $this->ApiReturn(0, '商品不存在');
    //     //查验支付凭证
    //     $proof = DB::name('order')->where('ios_proof', $receipt_data)->where('pay_type', 3)->value('id');
    //     if ($proof && $proof['status']==2)  {$this->ApiReturn(0, '凭证已过期');}
    //     //验证收据
    //     //$res = $this->validate_applepay($receipt_data, false); //false沙盒 true正式
    //     $res = $this->validate_applepay_panduan($receipt_data, true); //false沙盒 true正式
    //     //adlog("res",$res);
    //     //////////////

    //     $mizuan = $goods['ios_mizuan'];
    //     /////////////
    //     if (intval($res['status']) == 0) {  //验证成功
    //         $ios_bundle_id = $this->getConfig('ios_bundle_id');
    //         // var_dump($res);die;
    //         if ($ios_bundle_id != $res['receipt']['bundle_id'])  $this->ApiReturn(0, '验签失败');
    //         //adlog("苹果标识",$ios_bundle_id);
    //         // $goods_id='mizuan'.$goods['price'];
    //         $g_id = $res['receipt']['in_app'][0]['product_id'];
    //         if ($goods_id != $g_id)  $this->ApiReturn(0, '参数错误');
    //         //查验支付流水号
    //         $transaction_id = $res['receipt']['in_app'][0]['transaction_id'];
    //         $proof_transaction = DB::name('order')->where('ios_transaction_id', $transaction_id)->where('pay_type', 3)->find();
    //         if ($proof_transaction && $proof_transaction['status']==2) { $this->ApiReturn(0, '订单已被使用');}

    //         $ratio = $this->getConfig('ratio');
    //         if($proof_transaction){
    //             DB::name('order')->where('id',$proof_transaction['id'])->update(['status'=>2]);
    //         }else{
    //             $arr['order_no'] = getOrderNo();
    //             $arr['user_id'] = $user_id;
    //             $arr['mizuan'] = $mizuan;
    //             $arr['price'] = $goods['price'];
    //             $arr['pay_type'] = 3;
    //             $arr['status'] = 2;
    //             $arr['ios_proof'] = $receipt_data;
    //             $arr['ios_transaction_id'] = $transaction_id;
    //             $arr['addtime'] = $arr['paytime'] = time();
    //             //adlog("arr",$arr);
    //             DB::name('order')->insertGetId($arr);
    //         }

    //         //$mizuan = $goods['ios_mizuan'];
    //         // $mizuan=$goods['price'] * 7 + $send;

    //         if($a==1){
    //             userStoreInc($user_id, $mizuan, 11, 'klpls');
    //         }else{
    //             userStoreInc($user_id, $mizuan, 11, 'mizuan');
    //         }
    //         $this->ApiReturn(1, '购买成功');
    //     } else {  //验证失败
    //         $this->ApiReturn(0, '验证失败');
    //     }
    // }

    /**
     * IOS内购验证票据
     * @param string $receipt_data 付款后凭证
     * @return array                验证是否成功
     */
    private function validate_applepay($receipt_data, $sandbox = false)
    {
        // $apple_secret = config('applepay.apple_secret');
        $jsonData = array('receipt-data' => $receipt_data);
        //adlog("票据",$receipt_data);
        $post_json = json_encode($jsonData);
        //adlog("post_json",$post_curl);
        if ($sandbox) {
            $url = "https://buy.itunes.apple.com/verifyReceipt"; //正式环境
        } else {
            $url = "https://sandbox.itunes.apple.com/verifyReceipt"; //沙盒环境
        }
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_json);
        $result = curl_exec($ch);
        curl_close($ch);
        // var_dump(json_decode($result,true)['status']);die;
        return json_decode($result, true);
        //adlog("结果",$result);
    }
    //返回status示例:
    // * 0     验证成功
    // * 21000 App Store不能读取你提供的JSON对象
    // * 21002 receipt-data域的数据有问题
    // * 21003 receipt无法通过验证
    // * 21004 提供的shared secret不匹配你账号中的shared secret
    // * 21005 receipt服务器当前不可用
    // * 21006 receipt合法，但是订阅已过期。服务器接收到这个状态码时，receipt数据仍然会解码并一起发送
    // * 21007 receipt是Sandbox receipt，但却发送至生产系统的验证服务
    // * 21008 receipt是生产receipt，但却发送至Sandbox环境的验证服务


    private function validate_applepay_panduan($receipt_data, $sandbox)
    {
        $res = $this->validate_applepay($receipt_data, $sandbox);
        if ($res['status'] == '21007') {
            $res = $this->validate_applepay($receipt_data, false);
        } elseif ($res['status'] == '21008') {
            $res = $this->validate_applepay($receipt_data, true);
        }
        return $res;
    }


    public function upload_photo(Request $req)
    {
        $user_id = $req->param('user_id/s', '');
        $imgs    = $req->param('imgs/s', '');

        $img_arr = explode(',', $imgs);
        adlog('相册', $req->param(), '相册');
        $UPOBj = new \addons\qiniu\controller\Upl();
        $add   = [];
        foreach ($img_arr as $y) {
            $add[] = [
                'user_id'     => $user_id,
                'file_type'   => '1',
                'image'       => $y,
                'type_status' => '1',
                'coin'        => 0,
                'status'      => '2'
            ];
            $UPOBj->upload_excel('.' . $y);
        }
        // adlog('相册记录',$add,'相册');
        $R = Db::name('photo')->insertAll($add);
        // adlog('相册结果',$R,'相册');
        // adlog('相册sql',Db::getLastSql(),'相册');
    }


    public function upload_head(Request $req)
    {
        $user_id = $req->param('user_id/s', '');
        $head    = $req->param('head/s', '');
        $imgs    = $req->param('imgs/s', '');
        $img_arr = explode(',', $imgs);
        adlog('相册', $img_arr);
        adlog("看看有吗", $req->param());
        $UPOBj = new \addons\qiniu\controller\Upl();
        if ($imgs) {
            $add = [];
            foreach ($img_arr as $y) {
                $add[] = [
                    'user_id'     => $user_id,
                    'file_type'   => '1',
                    'image'       => $y,
                    'type_status' => '1',
                    'coin'        => 0,
                    'status'      => '2'
                ];
                $UPOBj->upload_excel('.' . $y);
            }
            adlog('相册记录', $add);
            $R = Db::name('photo')->insertAll($add);
        }
        $UPOBj->upload_excel('.' . $head);
        Db::name('user')->where('id', $user_id)->update(['avatar' => $head]);
    }


    public function imgs_upload(Request $req)
    {
        $imgs    = $req->param('imgs/s', '');
        $img_arr = explode(',', $imgs);
        adlog('相册', $req->param());
        $UPOBj = new \addons\qiniu\controller\Upl();
        $s     = [];
        foreach ($img_arr as $y) {
            $s = $UPOBj->upload_excel('.' . $y);
        }
        $this->success('', $s);
    }
}
