<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add,edit,del,import')}<!---->
                        <!--<a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>-->
                        <!--<a href="javascript:;" class="btn btn-success btn-add {:$auth->check('dynamic_cmt_ware_house/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>-->
                        <!--<a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('dynamic_cmt_ware_house/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>-->
                        <!--<a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('dynamic_cmt_ware_house/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>-->
                        

                        <!--<div class="dropdown btn-group {:$auth->check('dynamic_cmt_ware_house/multi')?'':'hide'}">-->
                        <!--    <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>-->
                        <!--    <ul class="dropdown-menu text-left" role="menu">-->
                        <!--        <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>-->
                        <!--        <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>-->
                        <!--    </ul>-->
                        <!--</div>-->

                        
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('dynamic_cmt_ware_house/edit')}"
                           data-operate-del="{:$auth->check('dynamic_cmt_ware_house/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
