<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use Exception;
use think\Db;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use app\common\library\Redis;
use think\Env;

/**
 * 问候语
 *
 * @icon fa fa-circle-o
 */
class Greeting extends Backend
{

    /**
     * Greeting模型对象
     * @var \app\admin\model\Greeting
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Greeting;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("onSwitchList", $this->model->getOnSwitchList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id', 'status', 'hot_word', 'on_switch']);

            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


    public function greeting_reset()
    {
        $li = Db::name('greeting')->where('on_switch', 1)->select();
        if ($li) {
            $prefix = Env::get('redis.redis_prefix');
            $redis  = (new Redis())->handler;
            $redis->del([$prefix . 'greeting_y', $prefix . 'greeting_x']);
            foreach ($li as $k => $v) {
                switch ($v['status']) {
                    case 1:
                        $redis->sadd($prefix . 'greeting_y', $v['hot_word']);
                        $redis->sadd($prefix . 'greeting_y_en', $v['en_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_ko', $v['ko_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_ja', $v['ja_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_hk', $v['hk_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_de', $v['de_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_fr', $v['fr_hot_word']);
                        break;
                    case 2:
                        $redis->sadd($prefix . 'greeting_x', $v['hot_word']);
                        $redis->sadd($prefix . 'greeting_x_en', $v['en_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_ko', $v['ko_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_ja', $v['ja_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_hk', $v['hk_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_de', $v['de_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_fr', $v['fr_hot_word']);
                        break;
                    case 3:
                        $redis->sadd($prefix . 'greeting_y', $v['hot_word']);
                        $redis->sadd($prefix . 'greeting_y_en', $v['en_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_ko', $v['ko_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_ja', $v['ja_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_hk', $v['hk_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_de', $v['de_hot_word']);
                        $redis->sadd($prefix . 'greeting_y_fr', $v['fr_hot_word']);
                        $redis->sadd($prefix . 'greeting_x', $v['hot_word']);
                        $redis->sadd($prefix . 'greeting_x_en', $v['en_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_ko', $v['ko_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_ja', $v['ja_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_hk', $v['hk_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_de', $v['de_hot_word']);
                        $redis->sadd($prefix . 'greeting_x_fr', $v['fr_hot_word']);
                        break;
                }
            }
        }
    }


    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name     = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->greeting_reset();
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name     = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->greeting_reset();
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk       = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->greeting_reset();
        $this->error(__('No rows were deleted'));
    }

}
