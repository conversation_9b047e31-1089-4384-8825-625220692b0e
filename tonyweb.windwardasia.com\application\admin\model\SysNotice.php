<?php

namespace app\admin\model;

use think\Model;


class SysNotice extends Model
{

    

    

    // 表名
    protected $name = 'sys_notice';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'msg_status_text'
    ];
    

    
    public function getMsgStatusList()
    {
        return ['0' => __('Msg_status 0'), '1' => __('Msg_status 1')];
    }


    public function getMsgStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['msg_status']) ? $data['msg_status'] : '');
        $list = $this->getMsgStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
