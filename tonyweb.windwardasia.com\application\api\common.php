<?php
if(!function_exists(('get_avatar'))){
    function get_avatar($url){
        if($url==false){$url = '/assets/img/avatar.png';}
        if(!preg_match('/^(http:\/\/|https:\/\/)/', $url)){
            //$cdnurl = \think\Config::get('upload.cdnurl');
            $cdnurl = 'https://img.topsweeter.com/';
            return $cdnurl . $url;
            // if($cdnurl!=''){
            //     return $cdnurl . $url;
            // }else{
            //     return request()->domain().$url;
            // }
        }else{
            return $url;
        }
    }
}