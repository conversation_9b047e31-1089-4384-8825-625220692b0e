<?php

namespace app\admin\model;

use think\Model;


class GiftLog extends Model
{

    

    

    // 表名
    protected $name = 'gift_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function user()
    {
        return $this->belongsTo('User', 'from_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    public function touser()
    {
        return $this->belongsTo('User', 'to_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function gift()
    {
        return $this->belongsTo('Gift', 'gift_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
