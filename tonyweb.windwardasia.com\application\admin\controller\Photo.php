<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Request;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;

/**
 * 相册管理
 *
 * @icon fa fa-photo
 */
class Photo extends Backend
{

    /**
     * Photo模型对象
     * @var \app\admin\model\Photo
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Photo;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("fileTypeList", $this->model->getFileTypeList());
        $this->view->assign("typeStatusList", $this->model->getTypeStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


                    

    /**
     * 查看
     */
    public function index()
    {
        $req = Request::instance();
        $user_id = $req->param('user_id','');
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user'])
                    ->where(function($sql)use($user_id){
                        if($user_id){
                            $sql->where('user_id',$user_id);
                        }
                        return $sql;
                    })
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','file_type','image','type_status','coin','status']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['nickname']);
				$ext = explode('.',$row->image)[1]??$row->image;
				$row->ext = $ext;
				$row->image = cdnurl($row->image,true);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
    
    public function del($ids = null){
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::name('photo_log')->where('photo_id','in', $ids)->delete();
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

}
