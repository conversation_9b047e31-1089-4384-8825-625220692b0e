<?php

namespace app\partner\model;

use think\Model;


class UnlockChat extends Model
{

    

    

    // 表名
    protected $name = 'unlock_chat';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'nickname'
    ];
    

    
    public function touser(){
        return $this->belongsTo('User', 'to_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function fromuser(){
        return $this->belongsTo('User', 'from_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
