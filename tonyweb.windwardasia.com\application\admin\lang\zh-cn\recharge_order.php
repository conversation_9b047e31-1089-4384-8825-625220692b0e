<?php

return [
    'Id'            => '主键ID',
    'Order_sn'      => '订单ID',
    'User_id'       => '会员ID',
    'Amount'        => '订单金额',
    'Paytype'       => '支付类型',
    'Paytype 1'     => '支付宝',
    'Paytype 2'     => '微信',
    'Paytype 3'     => '苹果内购',
    'Paytype 4'     => '余额',
    'Paytype -'     => '无',
    'Payamount'     => '支付金额',
    'Diamond'       => '获得天使币数',
    'Paytime'       => '支付时间',
    'Memo'          => '备注',
    'Createtime'    => '添加时间',
    'Updatetime'    => '更新时间',
    'Status'        => '状态',
    'Status 0'      => '待支付',
    'Status 1'      => '已支付',
    'Status 2'      => '过期失效',
    'Out_sn'        => '外部交易号',
    'User.nickname' => '充值人'
];
