<?php

namespace app\common\library;
/**
 * Created by Notepad++
 * User: UCPAAS NickLuo
 * Date: 2017/11/09
 * Time: 08:28
 * Dec : ucpass php sdk
 */
class Ucpaas
{
    //API请求地址
    const BaseUrl = "http://open2.ucpaas.com/sms-server/";
    
    //开发者账号。
    public $clientid;

    //开发者账号密码
    public $password;
    
    public function  __construct($options)
    {
        if (is_array($options) && !empty($options)) {
            $this->clientid = isset($options['clientid']) ? $options['clientid'] : '';
            $this->password = isset($options['password']) ? $options['password'] : '';
        } else {
            throw new Exception("非法参数");
        }
    }

    /**
     * @param $url    请求链接
     * @param $body   post数据
     * @param $method post或get
     * @return mixed|string
     */
	 
    private function connection($url, $body,$method)
    {
       
        $header = array(
            'Accept:application/json',
            'Content-Type:application/json;charset=utf-8',
        );
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        if($method == 'post'){
            curl_setopt($ch,CURLOPT_POST,1);
            curl_setopt($ch,CURLOPT_POSTFIELDS,$body);
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $result = curl_exec($ch);
        curl_close($ch);        
        return $result;
    }

    /**
	单条发送短信的function，适用于注册/找回密码/认证/操作提醒等单个用户单条短信的发送场景
     * @param $mobile       接收短信的手机号码
     * @param $templateid   短信模板，可在后台短信产品→选择接入的应用→短信模板-模板ID，查看该模板ID
     * @param null $param   变量参数，多个参数使用英文逗号隔开（如：param=“a,b,c”）
     * @param $uid			用于贵司标识短信的参数，按需选填。
     * @return mixed|string 
     * @throws Exception
     */
    public function SendSms($templateid,$param=null,$mobile,$uid){
        $url = self::BaseUrl . 'templatesms';       
        $body_json = array(
            'clientid'=>$this->clientid,
            'password'=>$this->password,
            'templateid'=>$templateid,
			'param'=>$param,
			'mobile'=>$mobile,
			'uid'=>$uid,
        );
        $body = json_encode($body_json);
        $data = $this->connection($url, $body,'post');
        return $data;
    }
	
	 /**
	 群发送短信的function，适用于运营/告警/批量通知等多用户的发送场景
     * @param $appid        应用ID
     * @param $mobileList   接收短信的手机号码，多个号码将用英文逗号隔开，如“18088888888,15055555555,13100000000”
     * @param $templateid   短信模板，可在后台短信产品→选择接入的应用→短信模板-模板ID，查看该模板ID
     * @param null $param   变量参数，多个参数使用英文逗号隔开（如：param=“a,b,c”）
     * @param $uid			用于贵司标识短信的参数，按需选填。
     * @return mixed|string 
     * @throws Exception
     */
	public function SendSms_Batch($appid,$templateid,$param=null,$mobileList,$uid){
        $url = self::BaseUrl . 'sendsms_batch';
        $body_json = array(
            'clientid'=>$this->clientid,
            'password'=>$this->password,
            'templateid'=>$templateid,
			'param'=>$param,
			'mobile'=>$mobileList,
			'uid'=>$uid,
        );
        $body = json_encode($body_json);
        $data = $this->connection($url, $body,'post');
        return $data;
    }
} 