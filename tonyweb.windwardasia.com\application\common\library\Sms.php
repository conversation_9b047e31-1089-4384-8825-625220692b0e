<?php

namespace app\common\library;

use fast\Random;
use think\Hook;
use \app\common\library\Ucass;
/**
 * 短信验证码类
 */
class Sms
{

    /**
     * 验证码有效时长
     * @var int
     */
    protected static $expire = 300;

    /**
     * 最大允许检测的次数
     * @var int
     */
    protected static $maxCheckNums = 10;

    /**
     * 获取最后一次手机发送的数据
     *
     * @param   int    $mobile 手机号
     * @param   string $event  事件
     * @return  Sms
     */
    public static function get($mobile, $event = 'default')
    {
        $sms = \app\common\model\Sms::
        where(['mobile' => $mobile, 'event' => $event])
            ->order('id', 'DESC')
            ->find();
        Hook::listen('sms_get', $sms, null, true);
        return $sms ? $sms : null;
    }

    // /**
    //  * 发送验证码
    //  *
    //  * @param   int    $mobile 手机号
    //  * @param   int    $code   验证码,为空时将自动生成4位数字
    //  * @param   string $event  事件
    //  * @return  boolean
    //  */
    // public static function send($mobile, $code = null, $event = 'default')
    // {
    //     $code = is_null($code) ? Random::numeric(config('captcha.length')) : $code;
    //     $time = time();
    //     $ip = request()->ip();
    //     $sms = \app\common\model\Sms::create(['event' => $event, 'mobile' => $mobile, 'code' => $code, 'ip' => $ip, 'createtime' => $time]);
    //     // $result = Hook::listen('sms_send', $sms, null, true);
    //     $templateid = "2854";    //可在后台短信产品→选择接入的应用→短信模板-模板ID，查看该模板ID
    //     $param = $code; //多个参数使用英文逗号隔开（如：param=“a,b,c”），如为参数则留空
    //     // $mobile = $mobile;
    //     $uid = "";
    //     //初始化必填
    //     //填写在开发者帐号
    //     $options['clientid']='b1ltg0';
    //     //填写在开发者密码
    //     $options['password']=md5('12345678');
    //     //初始化 $options必填
    //     $ucpass = new Ucpaas($options);
    //     //70字内（含70字）计一条，超过70字，按67字/条计费，超过长度短信平台将会自动分割为多条发送。分割后的多条短信将按照具体占用条数计费。
    //     $result = $ucpass->SendSms($templateid,$param,$mobile,$uid);
    //     $result = json_decode($result,true);
    //     if ($result['msg']!='成功') {
    //         $sms->delete();
    //         return false;
    //     }
    //     return true;
    // }

/**
     * 发送验证码
     *
     * @param   int    $mobile 手机号
     * @param   int    $code   验证码,为空时将自动生成4位数字
     * @param   string $event  事件
     * @return  boolean
     */
    public static function send($mobile, $code = null, $event = 'default')
    {
        $code = is_null($code) ? Random::numeric(config('captcha.length')) : $code;
        $time = time();
        $ip = request()->ip();
        $sms = \app\common\model\Sms::create(['event' => $event, 'mobile' => $mobile, 'code' => $code, 'ip' => $ip, 'createtime' => $time]);
        $result = Hook::listen('sms_send', $sms, null, true);
      
        $result = file_get_contents("https://api.smsbao.com/wsms?u=nuanliao666888&p=1c58b8f3b30040a68ad81730da59ab08&m=$mobile&c=【暖聊】您的验证码是{$code}。如非本人操作，请忽略本短信");
        if ($result !='0') {
            $sms->delete();
            return false;
        }
        return true;
    }
    /**
     * 发送通知
     *
     * @param   mixed  $mobile   手机号,多个以,分隔
     * @param   string $msg      消息内容
     * @param   string $template 消息模板
     * @return  boolean
     */
    public static function notice($mobile, $msg = '', $template = null)
    {
        $params = [
            'mobile'   => $mobile,
            'msg'      => $msg,
            'template' => $template
        ];
        $result = Hook::listen('sms_notice', $params, null, true);
        return $result ? true : false;
    }

    /**
     * 校验验证码
     *
     * @param   int    $mobile 手机号
     * @param   int    $code   验证码
     * @param   string $event  事件
     * @return  boolean
     */
    public static function check($mobile, $code, $event = 'default')
    {
        $time = time() - self::$expire;
        $sms = \app\common\model\Sms::where(['mobile' => $mobile, 'event' => $event])
            ->order('id', 'DESC')
            ->find();
        if ($sms) {
            if ($sms['createtime'] > $time && $sms['times'] <= self::$maxCheckNums) {
                $correct = $code == $sms['code'];
                if (!$correct) {
                    $sms->times = $sms->times + 1;
                    $sms->save();
                    return false;
                } else {
                    $result = Hook::listen('sms_check', $sms, null, true);
                    return $result;
                }
            } else {
                // 过期则清空该手机验证码
                self::flush($mobile, $event);
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 清空指定手机号验证码
     *
     * @param   int    $mobile 手机号
     * @param   string $event  事件
     * @return  boolean
     */
    public static function flush($mobile, $event = 'default')
    {
        \app\common\model\Sms::
        where(['mobile' => $mobile, 'event' => $event])
            ->delete();
        Hook::listen('sms_flush');
        return true;
    }
}
