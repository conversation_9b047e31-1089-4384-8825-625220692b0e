<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('发送消息用户ID')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-from_id"  class="form-control " name="row[from_id]" type="text" disabled value="{$row.from_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('接收消息用户ID')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-to_id"  class="form-control " name="row[to_id]" disabled  type="text" value="{$row.to_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('接收消息用户昵称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname"  class="form-control " name="row[nickname]" disabled type="text" value="{$row.nickname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('消息内容')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-text"  class="form-control " name="row[text]"   rows="5" cols="30" disabled type="text" >{$row.text|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('时间')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-createtime"  class="form-control " name="row[createtime]"  disabled type="text" value="{:$row.createtime?datetime($row.createtime):'无'}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
