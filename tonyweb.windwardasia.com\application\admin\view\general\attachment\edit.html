

<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label for="c-url" class="control-label col-xs-12 col-sm-2">{:__('Category')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select name="row[category]" class="form-control">
                <option value="">{:__('Please select category')}</option>
                {foreach name="categoryList" id="item"}
                <option value="{$key}" {if $key==$row.category}selected{/if}>{$item}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="c-url" class="control-label col-xs-12 col-sm-2">{:__('Url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[url]" value="{$row.url|htmlentities}"  id="c-url" class="form-control" required />
        </div>
    </div>
    <div class="form-group">
        <label for="c-imagewidth" class="control-label col-xs-12 col-sm-2">{:__('Imagewidth')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[imagewidth]" value="{$row.imagewidth}"  id="c-imagewidth" class="form-control" required />
        </div>
    </div>
    <div class="form-group">
        <label for="c-imageheight" class="control-label col-xs-12 col-sm-2">{:__('Imageheight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[imageheight]" value="{$row.imageheight}"  id="c-imageheight" class="form-control" required />
        </div>
    </div>
    <div class="form-group">
        <label for="c-imagetype" class="control-label col-xs-12 col-sm-2">{:__('Imagetype')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[imagetype]" value="{$row.imagetype|htmlentities}"  id="c-imagetype" class="form-control" required />
        </div>
    </div>
    <div class="form-group">
        <label for="c-imageframes" class="control-label col-xs-12 col-sm-2">{:__('Imageframes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" name="row[imageframes]" value="{$row.imageframes}"  id="c-imageframes" class="form-control" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-filename" class="control-label col-xs-12 col-sm-2">{:__('Filename')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[filename]" value="{$row.filename|htmlentities}"  id="c-filename" class="form-control" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-filesize" class="control-label col-xs-12 col-sm-2">{:__('Filesize')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" name="row[filesize]" value="{$row.filesize}"  id="c-filesize" class="form-control" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-mimetype" class="control-label col-xs-12 col-sm-2">{:__('Mimetype')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[mimetype]" value="{$row.mimetype|htmlentities}"  id="c-mimetype" class="form-control" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-extparam" class="control-label col-xs-12 col-sm-2">{:__('Extparam')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[extparam]" value="{$row.extparam|htmlentities}"  id="c-extparam" class="form-control" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-uploadtime" class="control-label col-xs-12 col-sm-2">{:__('Uploadtime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="datetime" name="row[uploadtime]" value="{$row.uploadtime|datetime}"  id="c-uploadtime" class="form-control datetimepicker" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-storage" class="control-label col-xs-12 col-sm-2">{:__('Storage')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[storage]" value="{$row.storage}"  id="c-storage" class="form-control" />
        </div>
    </div>
    <div class="form-group hide layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
