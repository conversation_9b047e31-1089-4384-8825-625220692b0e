<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/addons/cos/library" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/addons/cos/library/Guzzle/command/src" isTestSource="false" packagePrefix="GuzzleHttp\Command\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/cos/library/Guzzle/command/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/addons/cos/library/Guzzle/guzzle-services/src" isTestSource="false" packagePrefix="GuzzleHttp\Command\Guzzle\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/cos/library/Guzzle/guzzle-services/tests" isTestSource="true" packagePrefix="GuzzleHttp\Tests\Command\Guzzle\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/cos/library/Guzzle/guzzle-services/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/addons/cos/library/Guzzle/uri-template/src" isTestSource="false" packagePrefix="GuzzleHttp\UriTemplate\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/cos/library/Guzzle/uri-template/tests" isTestSource="true" packagePrefix="GuzzleHttp\UriTemplate\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/cos/library/Guzzle/uri-template/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/addons/qiniu/library" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/application" isTestSource="false" packagePrefix="app" />
      <sourceFolder url="file://$MODULE_DIR$/extend" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/extend/GatewayWorker/./Applications/YourApp" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/thinkphp/library/think" isTestSource="false" packagePrefix="think\" />
      <sourceFolder url="file://$MODULE_DIR$/thinkphp/tests" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/extend/GatewayWorker/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/extend/GatewayWorker/vendor/workerman/gateway-worker" />
      <excludeFolder url="file://$MODULE_DIR$/extend/GatewayWorker/vendor/workerman/workerman" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/adbario/php-dot-notation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/cloudauth-20190307" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/credentials" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/darabonba-openapi" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/dypnsapi-20170525" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/endpoint-util" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/gateway-spi" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/openapi-util" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/openplatform-20191219" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/tea" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/tea-fileform" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/tea-oss-sdk" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/tea-oss-utils" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/tea-utils" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/tea-xml" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/easywechat-composer/easywechat-composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ezyang/htmlpurifier" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/karsonzhang/fastadmin-addons" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lizhichao/one-sm" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/maennchen/zipstream-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/php-enum" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nelexa/zip" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/pinyin" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/socialite" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/wechat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/pimple/pimple" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php73" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/psr-http-message-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/tencentcloud/tencentcloud-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/framework" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-captcha" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-helper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-installer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-queue" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/txthinking/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/yansongda/pay" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/yansongda/supports" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>