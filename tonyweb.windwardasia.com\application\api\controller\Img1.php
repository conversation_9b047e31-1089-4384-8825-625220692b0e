<?php

namespace app\api\controller;
// namespace AlibabaCloud\SDK\Sample\Verify\LRFR;
use app\common\controller\Api;

use think\Request;
use think\Db;
use think\Config as ConfigModel;
use think\Env;


use AlibabaCloud\Credentials\Credential;
use AlibabaCloud\SDK\Cloudauth\*********\Cloudauth;
use AlibabaCloud\SDK\Cloudauth\*********\Models\LivenessFaceVerifyRequest;
use AlibabaCloud\SDK\Cloudauth\*********\Models\LivenessFaceVerifyResponse;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\Tea\Utils\Utils;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use Exception;

class Img1 extends Api
{

    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];



    /**
     * createClient
     * @param string $endpoint
     * @return Cloudauth
     */
    public static function createClient(string $endpoint): Cloudauth
    {
        // 阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
        // 强烈建议不要把AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。
        // 本示例通过阿里云Credentials工具从环境变量中读取AccessKey，来实现API访问的身份验证。如何配置环境变量，请参见 https://help.aliyun.com/document_detail/311677.html。 
        // $client = self::createClient(Env::get('redis.ALIBABA_CLOUD_ACCESS_KEY_ID'), Env::get('redis.ALIBABA_CLOUD_ACCESS_KEY_SECRET'));
        $credential = new Credential(['access_key_id'     => Env::get('redis.access_key_id'),//'<ACCESS-KEY-ID>',
                                      'access_key_secret' => Env::get('redis.access_key_secret'),//'<ACCESS-KEY-SECRET>'
        ]);
        $config     = new Config([
            // 使用credential配置凭证
            "credential" => $credential,
            // 设置HTTP代理。
            // "httpProxy" => "<http://xx.xx.xx.xx:xxxx>",
            // 设置HTTPS代理
            // "httpsProxy" => "<https://xx.xx.xx.xx:xxxx>",
            "endpoint"   => $endpoint,
        ]);
        return new Cloudauth($config);
    }

    /**
     * @return void
     */
    public static function main($str)
    {
        $request = new LivenessFaceVerifyRequest([
            // 请输入场景ID。
            "sceneId"             => 1000007728,
            "outerOrderNo"        => 'txgy' . time(),//"<商户请求的唯一标识>",
            // 要接入的认证方案，固定值：LR_FR_MIN。
            "productCode"         => 'LR_FR_MIN',//"<LR_FR_MIN>",
            // 活体检测类型。
            "model"               => 'FRONT_CAMERA_LIVENESS',//"<FRONT_CAMERA_LIVENESS>",

            // 设备Token，用于风险识别。
            // "deviceToken" => "<McozS1ZWRcRZStlERcZZo_QOytx5jcgZoZJEoRLOxxxxxxx>",
            // "mobile" => "<LR_FR_MIN>",
            // "ip" => "<114.xxx.xxx.xxx>",
            "userId"              => '',//"<123456789>",
            // 请在以下方式中选择一种，传入人脸图片。
            // 方式一：人脸图片Base64。
            "faceContrastPicture" => $str,//"<人脸图片Base64编码>",
            // 方式二：人脸图片OSS Bucket名和文件名。
            // "ossBucketName" => "<cn-shanghai-aliyun-cloudauth-xxxxx>",
            // "ossObjectName" => "<verify/xxxxx/xxxxxx.jpeg>",
            // 方式三：人脸图片的OSS URL地址。该地址必须公网可访问。
            // "faceContrastPictureUrl" => "<https://cn-shanghai-aliyun-cloudauth-xxxxxx.oss-cn-shanghai.aliyuncs.com/verify/xxxxx/xxxxx.jpeg>"
        ]);
        // 推荐，支持服务路由。
        $response = self::livenessFaceVerifyAutoRoute($request);
        // 不支持服务自动路由。
        // $response = self::livenessFaceVerify("cloudauth.cn-shanghai.aliyuncs.com", $request);
        if (!isset($response->body)) {
            return;
        };
        var_dump($response->body->requestId);
        var_dump($response->body->code);
        var_dump($response->body->message);
        var_dump($response->body->resultObject->materialInfo);
        var_dump($response->body->resultObject->subCode);
        var_dump($response->body->resultObject->passed);
    }

    /**
     * livenessFaceVerifyAutoRoute
     * @param LivenessFaceVerifyRequest $request
     * @return LivenessFaceVerifyResponse|null
     */
    public static function livenessFaceVerifyAutoRoute(LivenessFaceVerifyRequest $request): ?LivenessFaceVerifyResponse
    {
        $endpoints = [
            "cloudauth.cn-shanghai.aliyuncs.com",
            "cloudauth.cn-beijing.aliyuncs.com"
        ];
        foreach ($endpoints as $endpoint) {
            try {
                $response = self::livenessFaceVerify($endpoint, $request);
                if (Utils::equalNumber(500, $response->statusCode)) {
                    continue;
                }
                if (Utils::equalString("500", $response->body->code)) {
                    continue;
                }
                return $response;
            } catch (Exception $err) {
                var_dump($err->getCode());
                var_dump($err->getMessage());
            }
        }
        return null;
    }

    /**
     * livenessFaceVerify
     * @param string $endpoint
     * @param LivenessFaceVerifyRequest $request
     * @return LivenessFaceVerifyResponse
     */
    public static function livenessFaceVerify(string $endpoint, LivenessFaceVerifyRequest $request): LivenessFaceVerifyResponse
    {
        $client = self::createClient($endpoint);
        // 创建RuntimeObject实例并设置运行参数。
        $runtime                 = new RuntimeOptions([]);
        $runtime->readTimeout    = 10000;
        $runtime->connectTimeout = 10000;
        return $client->livenessFaceVerifyWithOptions($request, $runtime);
    }

    // array(3) {
//   ["faceAttack"] => string(1) "F"
//   ["faceOcclusion"] => string(1) "F"
//   ["facialPictureFront"] => array(2) {
//     ["faceAttackScore"] => float(4.9293041229248E-5)
//     ["qualityScore"] => float(2.9802322387695E-6)
//   }

    //   Picture OuterOrderNo ProductCode SceneId   UserId
    function index()
    {
        // halt(Env::get('redis.ALIBABA_CLOUD_ACCESS_KEY_ID'));
        // $str = '{"faceAttack":"F","faceOcclusion":"F","facialPictureFront":{"faceAttackScore":4.929304122924805E-5,"qualityScore":2.980232238769531E-6}}';
        // $str = '{"faceAttack":"F","faceOcclusion":"F","facialPictureFront":{"faceAttackScore":4.929304122924805E-5,"qualityScore":2.980232238769531E-6}}';
        // dump($arr = json_decode($str,true));
        // $arr['facialPictureFront']['faceAttackScore'] = number_format($arr['facialPictureFront']['faceAttackScore'], 2, '.', '');//floatval();
        // $arr['facialPictureFront']['qualityScore'] = number_format($arr['facialPictureFront']['qualityScore'], 2, '.', '');;//floatval($arr['facialPictureFront']['qualityScore']);
        // dump($arr);
        //$url = 'https://img.tangxingongyuan.com/uploads/20230818/8b71e5dbcabf261030312b3af3b259d0.jpg';
        // $url = 'https://img.tangxingongyuan.com/uploads/20230818/b31951fe31827bb21227a201c879b33f.jpg';
        $url  = 'https://img.tangxingongyuan.com/uploads/20230818/30476eda206183ea5a71b50fbcd2ed51.jpg';
        $file = file_get_contents($url);
        $str  = base64_encode($file);//halt($str);
        // $arr = [];
        // // $arr['faceContrast'] = ;
        // $arr['faceContrastPicture'] = $str;
        // $arr['outerOrderNo'] = 'txgy'.time();
        // $arr['productCode'] = 'LR_FR_MIN';//"productCode" => "<LR_FR_MIN>",
        // $arr['sceneId'] = 1000007728;
        // // 活体检测类型。
        // $arr["model"] = "FRONT_CAMERA_LIVENESS";
        // $arr['userId'] = ;
        self::main($str);
    }

    function get_Score($Url)
    {
        // 需要设置环境变量 TENCENTCLOUD_SECRET_ID，值为示例的 AKIDz8krbsJ5yKBZQpn74WFkmLPx3*******
        $secretId = "";//getenv("TENCENTCLOUD_SECRET_ID");
        // 需要设置环境变量 TENCENTCLOUD_SECRET_KEY，值为示例的 Gu5t9xGARNpq86cd98joQYCN3*******
        $secretKey          = "";//getenv("TENCENTCLOUD_SECRET_KEY");
        $param["Nonce"]     = rand();
        $param["Timestamp"] = time();
        $param["Region"]    = "ap-chengdu";
        $param["SecretId"]  = $secretId;
        $param["Version"]   = "2020-03-03";
        $param["Action"]    = "DetectLiveFaceAccurate";
        // $param["InstanceIds.0"] = "ins-09dx96dg";
        // $param["Limit"] = 20;
        // $param["Offset"] = 0;
        $param['Url'] = $Url;
        ksort($param);

        $signStr = "GETiai.tencentcloudapi.com/?";
        foreach ($param as $key => $value) {
            $signStr = $signStr . $key . "=" . $value . "&";
        }
        $signStr = substr($signStr, 0, -1);

        $signature = base64_encode(hash_hmac("sha1", $signStr, $secretKey, true));
        //echo $signature.PHP_EOL;
        //need to install and enable curl extension in php.ini
        $param["Signature"] = $signature;
        $url                = "https://iai.tencentcloudapi.com/?" . http_build_query($param);
        //echo $url.PHP_EOL;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $output = curl_exec($ch);
        curl_close($ch);
        $arr = json_decode($output, true);
        return $arr;
    }

    public function check_user_img(Request $req)
    {
        $user_id   = $this->auth->id;
        $txy_times = $this->auth->txy_times;
        if ($txy_times <= 0) {
            $this->error('今日剩余认证次数已用完', '');
        }
        $img = $req->param('img/s', '');
        if ($img == '') {
            $this->error('空图');
        }
        $true_img = get_avatar($img);
        // 实例化一个证书对象，入参需要传入腾讯云账户secretId，secretKey
        //$cred = new Credential(getenv("TENCENTCLOUD_SECRET_ID"), getenv("TENCENTCLOUD_SECRET_KEY"));
        $rep = $this->get_Score($true_img);
        if (isset($rep['Response']['Error'])) {
            $this->success('', ['success' => 0, 'msg' => $rep['Response']['Error']['Message']]);
        } else {
            $Score = $rep['Response']['Score'];
        }

        $min = ConfigModel::get('site.living_organism_score');
        if ($min > $Score) {
            Db::name('user')->where('id', $user_id)->dec('txy_times', 1)->update();
            //$this->error('请上传现拍照片',$Score);
            $this->success('', ['success' => 0, 'msg' => '活体程度' . $Score . '分，小于限值' . $min]);
        }
        $save               = [];
        $save['img_auth']   = '1';
        $save['auth_image'] = $img;
        $R                  = Db::name('user')->where('id', $user_id)->update($save);
        if ($R !== false) {
            Db::name('user')->where('id', $user_id)->dec('txy_times', 1)->update();
            $this->success('', ['success' => 1, 'msg' => '']);
        } else {
            Db::name('user')->where('id', $user_id)->dec('txy_times', 1)->update();
            $this->error('操作失败', $Score);
        }
    }
}