<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="typeStatusList" item="vo"}
            <label for="row[type_status]-{$key}"><input id="row[type_status]-{$key}" name="row[type_status]" type="radio" value="{$key}" {in name="key" value="$row.type_status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('platform')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="PlatformList" item="vo"}
            <label for="row[platform]-{$key}"><input id="row[platform]-{$key}" name="row[platform]" type="radio" value="{$key}" {in name="key" value="$row.platform"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Newversion')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-newversion" class="form-control" name="row[newversion]" type="text" value="{$row.newversion|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-content" class="form-control" name="row[content]" type="text" value="{$row.content|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Downloadurl')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-downloadurl" class="form-control" name="row[downloadurl]" type="text" value="{$row.downloadurl|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="StatusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" data-rule="required" class="form-control" name="row[weigh]" type="number" value="{$row.weigh|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
