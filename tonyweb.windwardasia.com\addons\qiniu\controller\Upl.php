<?php

namespace addons\qiniu\controller;

use app\common\exception\UploadException;
use app\common\library\Upload;
use app\common\model\Attachment;
use Qiniu\Auth;
use Qiniu\Storage\ResumeUploader;
use <PERSON>iu\Storage\UploadManager;
use think\addons\Controller;
use think\Config;
use think\File;

/**
 * 七牛管理
 *
 */
class Upl extends Controller
{
    public $auth;
    public $uploadMgr;
    public $token;
    public $config;
    public function _initialize()
    {
        //跨域检测
        check_cors_request();
        parent::_initialize();
        Config::set('default_return_type', 'json');

        $this->config = get_addon_config('qiniu');
        $this->config['savekey'] = str_replace(['{year}', '{mon}', '{day}', '{filemd5}', '{.suffix}'], ['$(year)', '$(mon)', '$(day)', '$(etag)', '$(ext)'], $this->config['savekey']);

        // 构建鉴权对象
        $this->auth = new Auth($this->config['accessKey'], $this->config['secretKey']);
        // 生成上传 Token
        $this->token = $this->auth->uploadToken($this->config['bucket'], null, 3600, ['saveKey' => ltrim($this->config['savekey'], '/')]);
        // 初始化 UploadManager 对象并进行文件的上传。
        $this->uploadMgr = new UploadManager();
        
    }

    
    
    public function upload_excel($filePath=''){
        $attachment = null;
        $saveKey = substr($filePath,2);
        // try {
            // 调用 UploadManager 的 putFile 方法进行文件的上传。
            list($ret, $err) = $this->uploadMgr->putFile($this->token, $saveKey, $filePath);
            if ($err !== null) {throw new \Exception("上传失败");}
            @unlink($filePath);
            //成功不做任何操作
        // } catch (\Exception $e) {
        //     $this->error("上传失败");
        // }
        return $ret;
    }
}
