<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" class="form-control" data-rule="required" step="0.01" name="row[money]" type="number" value="{$row.money|htmlentities}">
        </div>
    </div>
    <!--<div class="form-group">-->
    <!--    <label class="control-label col-xs-12 col-sm-2">{:__('Handingfee')}:</label>-->
    <!--    <div class="col-xs-12 col-sm-8">-->
    <!--        <input id="c-handingfee" class="form-control" data-rule="required; settledmoney" step="0.01" name="row[handingfee]" type="number" value="{$row.handingfee|htmlentities}">-->
    <!--    </div>-->
    <!--</div>-->
    <!--<div class="form-group">-->
    <!--    <label class="control-label col-xs-12 col-sm-2">{:__('Taxes')}:</label>-->
    <!--    <div class="col-xs-12 col-sm-8">-->
    <!--        <input id="c-taxes" class="form-control" data-rule="required; settledmoney" step="0.01" name="row[taxes]" type="number" value="{$row.taxes|htmlentities}">-->
    <!--    </div>-->
    <!--</div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Settledmoney')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="text-danger" style="padding:8px 0;" id="c-settledmoney">￥{$row.settledmoney}</div>
        </div>
    </div>
    <!--<div class="form-group">-->
    <!--    <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>-->
    <!--    <div class="col-xs-12 col-sm-8">-->
    <!--        <input id="c-type" class="form-control" data-rule="required" name="row[type]" type="text" value="{$row.type|htmlentities}">-->
    <!--    </div>-->
    <!--</div>-->
    
    <!--<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
            {foreach name="typeList" item="vo"}
            <label for="row[type]-{$key}"><input id="row[type]-{$key}" name="row[type]" type="radio" value="{$key}" {in name="key" value="$row.type"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>

        </div>
    </div>-->
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account" class="form-control" data-rule="required" name="row[account]" type="text" value="{$row.account|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Realname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Memo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-memo" class="form-control" name="row[memo]" type="text" value="{$row.memo|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Orderid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-orderid" class="form-control" name="row[orderid]" type="text" value="{$row.orderid|htmlentities}" readonly disabled>
        </div>
    </div>
    <!--<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Transactionid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-transactionid" class="form-control" name="row[transactionid]" type="text" value="{$row.transactionid|htmlentities}" readonly disabled>
        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
<script>
    var row = {$row};
</script>
