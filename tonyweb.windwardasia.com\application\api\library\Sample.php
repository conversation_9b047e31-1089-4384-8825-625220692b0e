<?php

namespace app\api\library;

//use AlibabaCloud\SDK\Dypnsapi\V20170525\Dypnsapi;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
//use AlibabaCloud\Tea\Utils\Utils;

use Darabonba\OpenApi\Models\Config;
//use AlibabaCloud\SDK\Dypnsapi\V20170525\Models\GetMobileRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;


use AlibabaCloud\SDK\Cloudauth\V20190307\Cloudauth;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\DescribeFaceVerifyRequest;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\DescribeFaceVerifyResponse;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\InitFaceVerifyRequest;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\InitFaceVerifyResponse;
use AlibabaCloud\SDK\Dypnsapi\V20170525\Dypnsapi;
use AlibabaCloud\SDK\Dypnsapi\V20170525\Models\GetMobileRequest;
use AlibabaCloud\Tea\Utils\Utils;

class Sample {

    /**
     * 使用AK&SK初始化账号Client
     * @param string $accessKeyId
     * @param string $accessKeySecret
     * @return Dypnsapi Client
     */
    public static function createClient($accessKeyId, $accessKeySecret){
        $config = new Config([
            // 必填，您的 AccessKey ID
            "accessKeyId" => $accessKeyId,
            // 必填，您的 AccessKey Secret
            "accessKeySecret" =>$accessKeySecret
        ]);
        // 访问的域名
        $config->endpoint = "dypnsapi.aliyuncs.com";
        return new Dypnsapi($config);
    }

    /**
     * @param string[] $args
     * @return void
     */
    public static function main($AccessToken){
        // 工程代码泄露可能会导致AccessKey泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/311677.html
        $client = self::createClient("LTAI5tSAgAZWbcTh1HvXGiXd", "******************************");
        $getMobileRequest = new GetMobileRequest(['accessToken'=>$AccessToken]);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $client->getMobileWithOptions($getMobileRequest, $runtime);dump($client);
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 如有需要，请打印 error
            dump($error->message);
            Utils::assertAsString($error->message);
        }
    }
    
    
    /////
    public static function aclient()
    {
        $config = new \Darabonba\OpenApi\Models\Config([
            // 您的AccessKey ID。
            "accessKeyId" => "LTAI5tSAgAZWbcTh1HvXGiXd", // LTAI5tEh4EYgdt2e6w3Pz5Dw
            // 您的AccessKey Secret。
            "accessKeySecret" => "******************************", // ******************************
        ]);
        $config->endpoint = "dypnsapi.aliyuncs.com";
        return new Dypnsapi($config);
    }

    public function agetMobileAli($access_token)
    {
        $client = self::client();
        $getMobileRequest = new GetMobileRequest([
            "accessToken" => $access_token
        ]);
        $response = $client->getMobile($getMobileRequest);
        //print_r($response->body);exit;
        if ($response->body->code != 'OK') {
            $this->error('错误码:'.$response->body->code.'--'.$response->body->message);
        }
        return $response->body->getMobileResultDTO->mobile;

        /*$runtime = new Utils\RuntimeOptions([]);
        $client->getMobileWithOptions($getMobileRequest, $runtime);*/


    }
    /////
}
// $path = __DIR__ . \DIRECTORY_SEPARATOR . '..' . \DIRECTORY_SEPARATOR . 'vendor' . \DIRECTORY_SEPARATOR . 'autoload.php';
// if (file_exists($path)) {
//     require_once $path;
// }
// Sample::main(array_slice($argv, 1));