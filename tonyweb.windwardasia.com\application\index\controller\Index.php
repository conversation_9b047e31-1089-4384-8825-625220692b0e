<?php

namespace app\index\controller;

use app\common\controller\Frontend;

class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        $this->redirect("/OjRoBPiuFw.php");
//        return $this->view->fetch();
    }
    
    public function privacy(){
        
        $article = \app\admin\model\Article::get(['id' => 2]);
        return $article->content;
    }

}
