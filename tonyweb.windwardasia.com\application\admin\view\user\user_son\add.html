<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-gender" data-rule="required" min="0" class="form-control selectpicker" name="row[gender]">
                {foreach name="genderList" item="vo"}
                    <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Birthday')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-birthday" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[birthday]" type="text" value="{:date('Y-m-d')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bio')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-bio" class="form-control " rows="5" name="row[bio]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" data-rule="required" class="form-control" step="0.01" name="row[money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Son_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-son_money" data-rule="required" class="form-control" step="0.01" name="row[son_money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Score')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-score" data-rule="required" class="form-control" name="row[score]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wx_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wx_id" data-rule="required" data-source="wx/index" class="form-control selectpage" name="row[wx_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Height')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-height" min="0" class="form-control" name="row[height]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weight" min="0" class="form-control" name="row[weight]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Master_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-master_city" class="form-control" data-toggle="city-picker" name="row[master_city]" type="text" value=""></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Point_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-point_city" class="form-control" data-toggle="city-picker" name="row[point_city]" type="text" value=""></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lng')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lng" class="form-control" name="row[lng]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lat')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lat" class="form-control" name="row[lat]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_vip')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_vip" class="form-control selectpicker" name="row[is_vip]">
                {foreach name="isVipList" item="vo"}
                    <option value="{$key}" {in name="key" value="three"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vip_end_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vip_end_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[vip_end_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_auth')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_auth" class="form-control selectpicker" name="row[is_auth]">
                {foreach name="isAuthList" item="vo"}
                    <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('First_recharge')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-first_recharge" class="form-control selectpicker" name="row[first_recharge]">
                {foreach name="firstRechargeList" item="vo"}
                    <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Brand_new')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-brand_new" class="form-control selectpicker" name="row[brand_new]">
                {foreach name="brandNewList" item="vo"}
                    <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Girl_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-girl_switch" class="form-control selectpicker" name="row[girl_switch]">
                {foreach name="girlSwitchList" item="vo"}
                    <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('P_user')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-p_user" class="form-control" name="row[p_user]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wechat')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wechat" class="form-control" name="row[wechat]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Alipay')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-alipay" class="form-control" name="row[alipay]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Top_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-top_switch" class="form-control selectpicker" name="row[top_switch]">
                {foreach name="topSwitchList" item="vo"}
                    <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nearby_hide')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-nearby_hide" class="form-control selectpicker" name="row[nearby_hide]">
                {foreach name="nearbyHideList" item="vo"}
                    <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Distance_hide')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-distance_hide" class="form-control selectpicker" name="row[distance_hide]">
                {foreach name="distanceHideList" item="vo"}
                    <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Unlock_chat')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unlock_chat" min="0" class="form-control" name="row[unlock_chat]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Unlock_weixin')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unlock_weixin" min="0" class="form-control" name="row[unlock_weixin]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Unlock_chatup')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unlock_chatup" min="0" class="form-control" name="row[unlock_chatup]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Exit_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-exit_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[exit_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Have_exit')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-have_exit" class="form-control selectpicker" name="row[have_exit]">
                {foreach name="haveExitList" item="vo"}
                    <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('True_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-true_name" class="form-control" name="row[true_name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('True_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-true_code" class="form-control" name="row[true_code]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wechat_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wechat_name" class="form-control" name="row[wechat_name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Alipay_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-alipay_name" class="form-control" name="row[alipay_name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Son_num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-son_num" min="0" class="form-control" name="row[son_num]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Img_auth')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-img_auth" class="form-control selectpicker" name="row[img_auth]">
                {foreach name="imgAuthList" item="vo"}
                    <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Video_auth')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-video_auth" class="form-control selectpicker" name="row[video_auth]">
                {foreach name="videoAuthList" item="vo"}
                    <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Auth_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-auth_image" class="form-control" size="50" name="row[auth_image]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-auth_image" class="btn btn-danger faupload" data-input-id="c-auth_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-auth_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-auth_image" class="btn btn-primary fachoose" data-input-id="c-auth_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-auth_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-auth_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Auth_file')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-auth_file" class="form-control" size="50" name="row[auth_file]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-auth_file" class="btn btn-danger faupload" data-input-id="c-auth_file" data-multiple="false" data-preview-id="p-auth_file"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-auth_file" class="btn btn-primary fachoose" data-input-id="c-auth_file" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-auth_file"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-auth_file"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Txy_times')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-txy_times" class="form-control" name="row[txy_times]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Forever_on')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-forever_on" class="form-control selectpicker" name="row[forever_on]">
                {foreach name="foreverOnList" item="vo"}
                    <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Video_failure')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-video_failure" class="form-control" name="row[video_failure]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" class="form-control" size="50" name="row[avatar]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
