<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id}">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}" >
        </div>
    </div>
    
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-gender" data-rule="required" min="0" class="form-control selectpicker" name="row[gender]">
                {foreach name="genderList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.gender"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    
    <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Birthday')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-birthday" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[birthday]" type="text" value="{$row.birthday}">
            </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wx_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wx_id" class="form-control" name="row[wx_id]" type="text" value="{$row.wx_id|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Height')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-height" min="0" class="form-control" name="row[height]" type="number" value="{$row.height|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weight" min="0" class="form-control" name="row[weight]" type="number" value="{$row.weight|htmlentities}">
        </div>
    </div>
    
    <!--<div class="form-group">-->
    <!--    <label class="control-label col-xs-12 col-sm-2">{:__('Master_city')}:</label>-->
    <!--    <div class="col-xs-12 col-sm-8">-->
    <!--        <div class='control-relative'><input id="c-master_city" class="form-control" data-toggle="city-picker" name="row[master_city]" type="text" value="{$row.master_city|htmlentities}"></div>-->
    <!--    </div>-->
    <!--</div>-->
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Master_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-master_city" class="form-control" name="row[master_city]" type="text" value="{$row.master_city|htmlentities}">
        </div>
    </div>
    
    <!--<div class="form-group">-->
    <!--    <label class="control-label col-xs-12 col-sm-2">{:__('Point_city')}:</label>-->
    <!--    <div class="col-xs-12 col-sm-8">-->
    <!--        <div class='control-relative'><input id="c-point_city" class="form-control" data-toggle="city-picker" name="row[point_city]" type="text" value="{$row.point_city|htmlentities}"></div>-->
    <!--    </div>-->
    <!--</div>-->
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Point_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-point_city" class="form-control" name="row[point_city]" type="text" value="{$row.point_city|htmlentities}">
        </div>
    </div>
    
     <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lng')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lng" class="form-control" name="row[lng]" type="text" value="{$row.lng|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lat')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lat" class="form-control" name="row[lat]" type="text" value="{$row.lat|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Girl_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-girl_switch" class="form-control selectpicker" name="row[girl_switch]">
                {foreach name="girlSwitchList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.girl_switch"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Forever_on')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-forever_on" class="form-control selectpicker" name="row[forever_on]">
                {foreach name="foreverOnList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.forever_on"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nearby_hide')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-nearby_hide" class="form-control selectpicker" name="row[nearby_hide]">
                {foreach name="nearbyHideList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.nearby_hide"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Distance_hide')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-distance_hide" class="form-control selectpicker" name="row[distance_hide]">
                {foreach name="distanceHideList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.distance_hide"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>