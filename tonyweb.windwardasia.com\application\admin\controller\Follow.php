<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Request;
/**
 * 关注记录管理
 *
 * @icon fa fa-circle-o
 */
class Follow extends Backend
{

    /**
     * Follow模型对象
     * @var \app\admin\model\Follow
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Follow;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


                    

    /**
     * 查看
     */
    public function index()
    {   
        $req = Request::instance();
        $from_id = $req->param('from_id','');
        $to_id = $req->param('to_id','');
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            if($to_id){$with = ['fromuser'];}else{$with = ['touser'];}
            $list = $this->model
                    ->with($with)
                    ->where(function($sql)use($from_id,$to_id){
                        if($from_id){
                            $sql->where('from_id',$from_id);
                        }elseif($to_id){
                            $sql->where('to_id',$to_id);
                        }
                        return $sql;
                    })
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

                if($to_id){
                    foreach ($list as $row) {
                        $row->visible(['id','from_id','to_id','createtime']);
                        // $row->visible(['fromuser']);
                        // $row->getRelation('fromuser')->visible(['nickname']);
                        $row->nickname = $row->fromuser->nickname;
                    }
                }elseif($from_id){
                    foreach ($list as $row) {
                        $row->visible(['id','from_id','to_id','createtime']);
                        // $row->visible(['touser']);
                        // $row->getRelation('touser')->visible(['nickname']);
                        $row->nickname = $row->touser->nickname;
                    }
                }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

}
