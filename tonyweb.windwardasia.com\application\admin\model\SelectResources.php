<?php

namespace app\admin\model;

use think\Model;


class SelectResources extends Model
{

    

    

    // 表名
    protected $name = 'select_resources';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_text'
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
        });
    }

    
    public function getTypeList()
    {
        return ['characters' => __('Type characters'), 'relation' => __('Type relation'), 'occupation' => __('Type occupation'), 'label' => __('Type label')
        ,'country'=>'国籍','revenue'=>'年收入','time'=>'能够见面的时间','language'=>'常用的语言','meet_budget'=>'约会预算','car_budget'=>'车马费预算','weight'=>'体型'];
    }


    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }




}
