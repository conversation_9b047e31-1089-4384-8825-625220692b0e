<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\library\Auth;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use app\common\library\Redis;
use think\Env;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
/**
 * 评论库
 *
 * @icon fa fa-circle-o
 */
class DynamicCmtWarehouse extends Backend
{

    /**
     * DynamicCmtWareHouse模型对象
     * @var \app\admin\model\DynamicCmtWareHouse
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\DynamicCmtWarehouse;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model

                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','cmt_content']);

            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->update_redis();
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->update_redis();
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->update_redis();
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    public function import(){
        // ini_set('memory_limit','128M');
        ini_set('memory_limit',-1); //没有内存限制
        // dump($this->request->request());
        $file = $this->request->request('file');
        if (!$file) {
            $this->error(__('Parameter %s can not be empty', 'file'));
        }

        $imageFilePath = './uploads/'.date('Ymd').'/'; //图片本地存储的路径
        if (!file_exists($imageFilePath)) { //如果目录不存在则递归创建
            mkdir($imageFilePath, 0777, true);
        }
        // halt($imageFilePath);
        //加载打开文件 $file
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
        //这一行不要写，写了这个是获取不到图片这些数据的
        //$reader->setReadDataOnly(TRUE);
        $spreadsheet = $reader->load('.'.$file);
        $objWorksheet = $spreadsheet->getSheet(0);
        $data = $objWorksheet->toArray();//dump($data);
        // $UPOBj = new \addons\qiniu\controller\Upl();
        // //遍历
        // foreach ($objWorksheet->getDrawingCollection() as &$drawing) {
        //     list($startColumn, $startRow) = Coordinate::coordinateFromString($drawing->getCoordinates());//dump($startColumn);第几列    dump($startRow);第几行
        //     $imageFileName = $drawing->getCoordinates() . mt_rand(10000, 99999);

        //     switch ($drawing->getExtension()) {
        //         case 'jpg':
        //         case 'jpeg':
        //             //加了时间戳是为了不让文件名重复
        //             $imageFileName .= '_'.time().'.jpg';
        //             $source = imagecreatefromjpeg($drawing->getPath());
        //             imagejpeg($source, $imageFilePath . $imageFileName);$source = null;
        //             // $UPOBj->upload_excel($imageFilePath . $imageFileName);
        //             break;
        //         case 'gif':
        //             $imageFileName .= '_'.time().'.gif';
        //             $source = imagecreatefromgif($drawing->getPath());
        //             imagegif($source, $imageFilePath . $imageFileName);$source = null;
        //             break;
        //         case 'png':
        //             $imageFileName .= '_'.time().'.png';
        //             $source = imagecreatefrompng($drawing->getPath());
        //             imagepng($source, $imageFilePath. $imageFileName);$source = null;
        //             break;
        //          case 'webp':
        //             $imageFileName .= '_'.time().'.webp';
        //             $source = imagecreatefromwebp($drawing->getPath());
        //             imagewebp($source, $imageFilePath. $imageFileName);$source = null;
        //             break;
        //     }
        //     $startColumn = ABC2decimal($startColumn);
        //     //把保存成链接的文件放回拿到的数据中
        //     // $data[$startRow-1][$startColumn] = $this->request->domain() . $path . $imageFileName;
        //     $data[$startRow-1][$startColumn] = $imageFilePath . $imageFileName;
        //     $drawing = $source = $imageFileName = null;
        //     // $UPOBj->upload_excel();
        // }

        // unset($drawing);

        $prefix = Env::get('redis.redis_prefix');
        $redis = (new Redis())->handler;

        $time = time();
        $all = [];
        // dump($data);
        foreach($data as $k=>$v){
            $item = trim($v[0]);
            $if = $redis->sismember($prefix.'all_cmt_warehouse',$item);
            if(!$if){
                $all[] = ['cmt_content'=>$item];
            }
        }
        if(!empty($all)){
            Db::name('dynamic_cmt_warehouse')->insertAll($all);
            $this->update_redis();
        }
        $this->success('导入成功');
    }

    //添加数据
    public function update_redis(){
        $prefix = Env::get('redis.redis_prefix');
        $redis = (new Redis())->handler;
        $redis->del([$prefix.'all_cmt_warehouse']);
//        $old = $redis->smembers($prefix.'all_cmt_warehouse');
        $new = Db::name('dynamic_cmt_warehouse')->column('cmt_content');
//        $diff = array_diff($new,$old);
        if(!empty($new)){
            foreach ($new as $y){
                $rs = $redis->sadd($prefix.'all_cmt_warehouse',$y);
                adlog('更新',$rs);
            }
        }
        return ;
    }
}
