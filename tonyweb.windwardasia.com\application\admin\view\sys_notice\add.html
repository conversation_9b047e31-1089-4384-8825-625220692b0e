<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index_all" data-field="nickname" class="form-control selectpage" data-page-size="15" name="row[user_id]" type="text" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" class="form-control" name="row[title]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-content" class="form-control" name="row[content]" type="text" value="">
        </div>
    </div>
    <!--<div class="form-group">-->
    <!--    <label class="control-label col-xs-12 col-sm-2">{:__('Msg_status')}:</label>-->
    <!--    <div class="col-xs-12 col-sm-8">-->
            
    <!--        <div class="radio">-->
    <!--        {foreach name="msgStatusList" item="vo"}-->
    <!--        <label for="row[msg_status]-{$key}"><input id="row[msg_status]-{$key}" name="row[msg_status]" type="radio" value="{$key}" {in name="key" value="0"}checked{/in} /> {$vo}</label> -->
    <!--        {/foreach}-->
    <!--        </div>-->

    <!--    </div>-->
    <!--</div>-->
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
