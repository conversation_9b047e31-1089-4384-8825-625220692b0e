<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\Config;
use app\common\library\Redis;
use app\common\model\User;

class Crontab extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function unlock_score()
    {
        $result = 'successful';
        try {
            $list = Db::name('user_score_log')
                ->where([
                    'type' => '2',
                    'is_jubao' => '2',
                    'is_ice' => '2'
                ])
                ->where('endtime', '<', time())
                ->select();

            $userUpdates = [];
            $logIds = [];

            foreach ($list as $v) {
                $userInfo = User::where('id', $v['user_id'])->field('id,ice_score,from_score')->find();
                if ($userInfo) {
                    $ice_score = max(0, bcsub(strval($userInfo->ice_score), strval($v['score']), 2));
                    $score_to_add = $ice_score == 0 ? $userInfo->ice_score : $v['score'];
                    $after_from_score = bcadd(strval($userInfo->from_score), strval($score_to_add), 2);

                    $userUpdates[] = [
                        'id' => $userInfo->id,
                        'from_score' => $after_from_score,
                        'ice_score' => $ice_score
                    ];
                }
                $logIds[] = $v['id'];
            }

            if ($userUpdates) {
                (new User())->saveAll($userUpdates);
            }
            if ($logIds) {
                Db::name('user_score_log')->where('id', 'in', $logIds)->update(['is_ice' => 1]);
            }
        } catch (\Exception $e) {
        }
        return $result;
    }

    public function update_vip_end()
    {
        try {
            $ids = Db::name('user')->where([
                'is_vip' => 'one'
            ])->where('vip_end_time', '<', time())->column('id');

            if ($ids) {
                Db::name('user')->where('id', 'in', $ids)->update([
                    'is_vip' => 'three',
                    'vip_end_time' => 0,
                    'unlock_chat' => 0,
                    'unlock_weixin' => 0,
                    'unlock_chatup' => 0,
                    'vip_type' => 0
                ]);
            }
        } catch (\Exception $e) {
        }
    }

    public function update_txy_auth_times()
    {
        try {
            $txy_max_times = Config::get('site.txy_max_times');
            Db::name('user')->where([
                'img_auth' => '0'
            ])->where('txy_times', '<>', $txy_max_times)->update(['txy_times' => $txy_max_times]);
        } catch (\Exception $e) {
        }
    }

    public function update_unlock()
    {
        try {
            $configs = [
                'vip' => [
                    'unlock_chat' => Config::get('site.unlock_chat_num'),
                    'unlock_weixin' => Config::get('site.unlock_weixin_num'),
                    'unlock_chatup' => Config::get('site.unlock_chatup_vip'),
                    'pair_voice_leave' => Config::get("site.pair_limit_voice_vip"),
                    'pair_live_leave' => Config::get("site.pair_limit_live_vip")
                ],
                'girl' => [
                    'unlock_chat' => Config::get('site.unlock_chat_num_girl'),
                    'unlock_weixin' => Config::get('site.unlock_weixin_num_girl'),
                    'unlock_chatup' => Config::get('site.unlock_chatup_girl'),
                    'pair_voice_leave' => Config::get("site.pair_limit_voice_girl"),
                    'pair_live_leave' => Config::get("site.pair_limit_live_girl")
                ],
                'auth_female' => [
                    'unlock_chat' => Config::get('site.unlock_chat_num_x'),
                    'unlock_weixin' => Config::get('site.unlock_weixin_num_x'),
                    'unlock_chatup' => Config::get('site.unlock_chatup_x'),
                    'pair_voice_leave' => Config::get("site.pair_limit_voice_x"),
                    'pair_live_leave' => Config::get("site.pair_limit_live_x")
                ],
                'normal' => [
                    'unlock_chat' => 0,
                    'unlock_weixin' => 0,
                    'unlock_chatup' => Config::get('site.unlock_chatup_pub'),
                    'pair_voice_leave' => Config::get("site.pair_limit_voice_y"),
                    'pair_live_leave' => Config::get("site.pair_limit_live_y")
                ]
            ];

            $User = new User();
            Db::name('user')->field('id,is_vip,girl_switch,is_auth,gender')
                ->chunk(200, function ($list) use ($User, $configs) {
                    $updates = [];
                    foreach ($list as $user) {
                        $config = $configs['normal'];
                        if ($user['is_vip'] == 'one') {
                            $config = $configs['vip'];
                        } elseif ($user['girl_switch'] == '1') {
                            $config = $configs['girl'];
                        } elseif ($user['is_auth'] == '1' && $user['gender'] == '2') {
                            $config = $configs['auth_female'];
                        }
                        $updates[] = array_merge(['id' => $user['id']], $config);
                    }
                    if ($updates) {
                        $User->saveAll($updates);
                    }
                });
        } catch (\Exception $e) {
        }
    }


    public function del_chat_up()
    {
        try {
            $redis = (new Redis())->handler;
            $redis->select(5);
            $list = $redis->smembers('chat_up_all');
            if ($list) {
                foreach ($list as $user_id) {
                    $redis->del("chat_up_{$user_id}");
                }
                $redis->del('chat_up_all');
            }
        } catch (\Exception $e) {
        }
    }

    public function update_forever_on()
    {
        try {
            $list = Db::name('user')->where('forever_on', '1')->column('id');
            if ($list) {
                $redis = (new Redis())->handler;
                $redis->select(0);
                $time = strval(time());
                foreach ($list as $user_id) {
                    $redis->hset('global_online', strval($user_id), $time);
                    $redis->setex('on_line_' . $user_id, 300, 1);
                }
            }
        } catch (\Exception $e) {
        }
    }

    public function update_new_user()
    {
        try {
            $new_user_leng = Config::get('site.new_user_leng');
            Db::name('user')->where('brand_new', '1')
                ->whereTime('createtime', '<=', strtotime("- {$new_user_leng} days"))
                ->update(['brand_new' => '0']);
        } catch (\Exception $e) {
        }
    }


    public function girl_auth_return()
    {
        try {
            $list = Db::name('user')->where([
                'gender' => 2,
                'parent_return' => '1'
            ])->where('return_time is not null')
              ->whereTime('return_time', '<=', time())
              ->field('id,p_user')->select();

            if ($list) {
                $x_parent_money = Config::get('site.x_parent_money');
                foreach ($list as $y) {
                    if ($y['p_user']) {
                        \app\common\model\User::money($x_parent_money, $y['p_user'], '下级真人头像认证获得分佣');
                        Db::name('user')->where('id', $y['p_user'])
                            ->inc('son_money', $x_parent_money)
                            ->inc('son_num', 1)->update();
                        Db::name('user')->where('id', $y['id'])->update([
                            'if_effective' => '1',
                            'parent_return' => '3',
                            'return_time' => null
                        ]);
                    } else {
                        Db::name('user')->where('id', $y['id'])->update([
                            'if_effective' => '1',
                            'parent_return' => '4',
                            'return_time' => null
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
        }
    }

    public function del_user()
    {
        try {
            $list = Db::name('user')->where('have_exit', 2)
                ->whereTime('exit_time', '<=', time())->column('id');
            if ($list) {
                $updateData = [
                    'username' => '',
                    'nickname' => '已注销',
                    'mobile' => '',
                    'avatar' => '',
                    'gender' => 0,
                    'birthday' => '',
                    'bio' => '',
                    'status' => 'hidden',
                    'wx_openid' => '',
                    'nearby_hide' => '0',
                    'distance_hide' => '0',
                    'exit_time' => null,
                    'have_exit' => '1',
                    'true_name' => '',
                    'true_code' => '',
                    'wechat_name' => '',
                    'alipay_name' => '',
                ];
                Db::name('user')->where('id', 'in', $list)->update($updateData);
            }
        } catch (\Exception $e) {
        }
    }

    public function close_order()
    {
        try {
            $wait_pay_leng = Config::get('site.wait_pay_leng');
            $time_limit = strtotime("- {$wait_pay_leng} minutes");

            Db::name('vip_order')->where('status', '0')
                ->whereTime('createtime', '<=', $time_limit)
                ->update(['status' => '2']);

            Db::name('recharge_order')->where('status', '0')
                ->whereTime('createtime', '<=', $time_limit)
                ->update(['status' => '2']);
        } catch (\Exception $e) {
        }
    }

    public function update_on_line()
    {
        try {
            $redis = (new Redis())->handler;
            $redis->select(0);
            $time = time();
            User::chunk(100, function ($users) use ($redis, $time) {
                $updates = [];
                foreach ($users as $user) {
                    $near = $redis->hget('global_online', $user->id);
                    $on_line = ($time - $near > 180) ? '2' : '1';
                    $updates[] = ['id' => $user->id, 'on_line' => $on_line];
                }
                if ($updates) {
                    (new User())->saveAll($updates);
                }
            }, 'id', 'desc');
        } catch (\Exception $e) {
        }
    }
}