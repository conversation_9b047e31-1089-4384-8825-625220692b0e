<?php

namespace app\admin\model;

use think\Model;


class Photo extends Model
{

    

    

    // 表名
    protected $name = 'photo';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text',
        'file_type_text',
        'type_status_text',
        'ext'
    ];
    

    
    public function getFileTypeList(){
        return ['1' => __('File_type 1'), '2' => __('File_type 2')];
    }
    
    public function getStatusList(){
        return ['1' => __('Status 1'), '2' => __('Status 2'),'3' => __('Status 3')];
    }
    
    public function getTypeStatusList(){
        return ['1' => __('Type_status 1'), '2' => __('Type_status 2'), '3' => __('Type_status 3')];
    }


    public function getFileTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['file_type']) ? $data['file_type'] : '');
        $list = $this->getFileTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getTypeStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type_status']) ? $data['type_status'] : '');
        $list = $this->getTypeStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
