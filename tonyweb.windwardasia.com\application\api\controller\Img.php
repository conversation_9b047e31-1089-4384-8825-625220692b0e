<?php

namespace app\api\controller;
// namespace AlibabaCloud\SDK\Sample\Verify\LRFR;
use app\common\controller\Api;

use think\Request;
use think\Db;
use think\Config  as ConfigModel;
use think\Env;


use AlibabaCloud\SDK\Cloudauth\*********\Cloudauth;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Cloudauth\*********\Models\LivenessFaceVerifyRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

class Img extends Api{
    
    protected $noNeedLogin = [''];
    protected $noNeedRight = ['*'];
    

    /**
     * 使用AK&SK初始化账号Client
     * @param string $accessKeyId
     * @param string $accessKeySecret
     * @return Cloudauth Client
     */
    public static function createClient($accessKeyId, $accessKeySecret){
        $config = new Config([
            // 必填，您的 AccessKey ID
            "accessKeyId" => $accessKeyId,
            // 必填，您的 AccessKey Secret
            "accessKeySecret" => $accessKeySecret
        ]);
        // Endpoint 请参考 https://api.aliyun.com/product/Cloudauth
        $config->endpoint = "cloudauth.aliyuncs.com";
        return new Cloudauth($config);
    }

    /**
     * @param string[] $args
     * @return void
     */
    public static function main($arr){
        // 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例使用环境变量获取 AccessKey 的方式进行调用，仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/311677.html
        $client = self::createClient(Env::get('redis.access_key_id'), Env::get('redis.access_key_secret'));
        $livenessFaceVerifyRequest = new LivenessFaceVerifyRequest([
            "productCode" => "LR_FR_MIN",
            "sceneId" => 1000007728,
            "outerOrderNo" => $arr['user_id'],
            "userId" => $arr['user_id'],
            "faceContrastPicture" => $arr['str'],
        ]);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $client->livenessFaceVerifyWithOptions($livenessFaceVerifyRequest, $runtime);
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 如有需要，请打印 error
            Utils::assertAsString($error->message);
            return ['code'=>0,'msg'=>$error->message];
        }
        adlog('认证原信息',json_decode(json_encode($response),true));
        if ($response->body->code == 200 && $response->body->message == 'success') {
            // var_dump($response->body->requestId);
            // var_dump($response->body->code);
            // var_dump($response->body->message);
            // var_dump($response->body->resultObject->materialInfo);
            // var_dump($response->body->resultObject->subCode);
            // var_dump($response->body->resultObject->passed);
            return ['code'=>1,'msg'=>'','data'=>json_decode($response->body->resultObject->materialInfo,true)];
        }else{
            return ['code'=>0,'msg'=>'认证失败'];
        }
        
    }
    
    public function check_user_img(Request $req){
        $user_id = $this->auth->id;
        $txy_times = $this->auth->txy_times;
        if($txy_times<=0){$this->error('今日剩余认证次数已用完','');}
        $img = $req->param('img/s','');
        if($img==''){$this->error('空图');}
        $true_img = get_avatar($img);
        adlog('图片',$true_img);
        $file = file_get_contents($true_img);
        $str = base64_encode($file);//halt($str);
        $r = self::main(['user_id'=>$user_id,'str'=>$str]);
        adlog('认证结果',$r);
        if($r['code']==0){
            $this->error($r['msg']);
        }else{
            //{"faceAttack":"F","faceOcclusion":"F","facialPictureFront":{"faceAttackScore":0.6142578125,"qualityScore":99.47735595703125}}
            $Score = $similar_rate = intval($r['data']['facialPictureFront']['qualityScore']);
            $face_attack_score = $r['data']['facialPictureFront']['faceAttackScore'];
            $quality_score = $r['data']['facialPictureFront']['qualityScore'];
        }
        $save = [];
        $min = ConfigModel::get('site.living_organism_score');
        if($min>$Score){
            // Db::name('user')->where('id',$user_id)->dec('txy_times',1)->update();
            //$this->error('请上传现拍照片',$Score);
            // $this->success('',['success'=>0,'msg'=>'活体程度'.$Score.'分，小于限值'.$min]);
            $save['img_auth'] = '3';//照片小于阈值，照片认证失败
        }else{
            $save['img_auth'] = '1';
        }
        
        $save['auth_image'] = $img;
        $save['similar_rate'] = $similar_rate;
        $save['quality_score'] = $quality_score;
        $save['face_attack_score'] = $face_attack_score;
        $R = Db::name('user')->where('id',$user_id)->update($save);
        if($R!==false){
            Db::name('user')->where('id',$user_id)->dec('txy_times',1)->update();
            if($save['img_auth']!='1'){
                $this->success('',['success'=>0,'msg'=>'评分过低']);
            }else{
                $this->success('',['success'=>1,'msg'=>'']);
            }
        }else{
            Db::name('user')->where('id',$user_id)->dec('txy_times',1)->update();
            $this->error('操作失败',$Score);
        }
    }
}