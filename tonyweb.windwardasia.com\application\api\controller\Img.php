<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Request;
use think\Db;
use think\Config as ConfigModel;
use think\Env;
use AlibabaCloud\SDK\Cloudauth\*********\Cloudauth;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Cloudauth\*********\Models\LivenessFaceVerifyRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

class Img extends Api
{
    protected $noNeedLogin = [''];
    protected $noNeedRight = ['*'];

    public static function createClient($accessKeyId, $accessKeySecret)
    {
        $config = new Config([
            "accessKeyId" => $accessKeyId,
            "accessKeySecret" => $accessKeySecret
        ]);
        $config->endpoint = "cloudauth.aliyuncs.com";
        return new Cloudauth($config);
    }

    public static function main($arr)
    {
        $result = ['code' => 0, 'msg' => '认证失败'];
        try {
            $client = self::createClient(Env::get('redis.access_key_id'), Env::get('redis.access_key_secret'));
            $livenessFaceVerifyRequest = new LivenessFaceVerifyRequest([
                "productCode" => "LR_FR_MIN",
                "sceneId" => 1000007728,
                "outerOrderNo" => $arr['user_id'],
                "userId" => $arr['user_id'],
                "faceContrastPicture" => $arr['str'],
            ]);
            $runtime = new RuntimeOptions([]);
            $response = $client->livenessFaceVerifyWithOptions($livenessFaceVerifyRequest, $runtime);

            if ($response->body->code == 200 && $response->body->message == 'success') {
                $result = [
                    'code' => 1,
                    'msg' => '',
                    'data' => json_decode($response->body->resultObject->materialInfo, true)
                ];
            }
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            Utils::assertAsString($error->message);
            $result = ['code' => 0, 'msg' => $error->message];
        }
        return $result;
    }
    public function check_user_img(Request $req)
    {
        $user_id = $this->auth->id;
        $txy_times = $this->auth->txy_times;
        $response_data = ['success' => 0, 'msg' => '评分过低'];

        if ($txy_times <= 0) {
            $this->error('今日剩余认证次数已用完', '');
        }

        $img = $req->param('img/s', '');
        if ($img == '') {
            $this->error('空图');
        }

        try {
            $true_img = get_avatar($img);
            $file = file_get_contents($true_img);
            $str = base64_encode($file);
            $r = self::main(['user_id' => $user_id, 'str' => $str]);

            if ($r['code'] == 0) {
                $this->error($r['msg']);
            }

            $quality_score = $r['data']['facialPictureFront']['qualityScore'];
            $Score = intval($quality_score);
            $min = ConfigModel::get('site.living_organism_score');

            $save = [
                'auth_image' => $img,
                'similar_rate' => $Score,
                'quality_score' => $quality_score,
                'face_attack_score' => $r['data']['facialPictureFront']['faceAttackScore'],
                'img_auth' => ($min > $Score) ? '3' : '1'
            ];

            $R = Db::name('user')->where('id', $user_id)->update($save);
            if ($R !== false) {
                Db::name('user')->where('id', $user_id)->dec('txy_times', 1)->update();
                if ($save['img_auth'] == '1') {
                    $response_data = ['success' => 1, 'msg' => ''];
                }
            } else {
                Db::name('user')->where('id', $user_id)->dec('txy_times', 1)->update();
                $this->error('操作失败', $Score);
            }
        } catch (\Exception $e) {
            Db::name('user')->where('id', $user_id)->dec('txy_times', 1)->update();
            $this->error('操作失败');
        }

        $this->success('', $response_data);
    }
}