<?php

namespace addons\epay\controller;

use addons\epay\library\Service;
use addons\epay\library\Wechat;
use addons\third\model\Third;
use app\common\library\Auth;
use think\addons\Controller;
use think\Response;
use think\Session;
use Yansongda\Pay\Exceptions\GatewayException;
use Yansongda\Pay\Pay;
use think\Db;
use app\common\library\Redis;
//use think\Request;
use think\Config;
/**
 * API接口控制器
 *
 * @package addons\epay\controller
 */
class Api extends Controller
{

    protected $layout = 'default';
    protected $config = [];

    /**
     * 默认方法
     */
    public function index()
    {
        return;
    }

    /**
     * 外部提交
     */
    public function submit()
    {
        $this->request->filter('trim');
        $out_trade_no = $this->request->request("out_trade_no");
        $title = $this->request->request("title");
        $amount = $this->request->request('amount');
        $type = $this->request->request('type');
        $method = $this->request->request('method', 'web');
        $openid = $this->request->request('openid', '');
        $auth_code = $this->request->request('auth_code', '');
        $notifyurl = $this->request->request('notifyurl', '');
        $returnurl = $this->request->request('returnurl', '');

        if (!$amount || $amount < 0) {
            $this->error("支付金额必须大于0");
        }

        if (!$type || !in_array($type, ['alipay', 'wechat'])) {
            $this->error("支付类型错误");
        }

        $params = [
            'type'         => $type,
            'out_trade_no' => $out_trade_no,
            'title'        => $title,
            'amount'       => $amount,
            'method'       => $method,
            'openid'       => $openid,
            'auth_code'    => $auth_code,
            'notifyurl'    => $notifyurl,
            'returnurl'    => $returnurl,
        ];
        return Service::submitOrder($params);
    }

    /**
     * 微信支付(公众号支付&PC扫码支付)
     * @return string
     */
    public function wechat()
    {
        $config = Service::getConfig('wechat');

        $isWechat = stripos($this->request->server('HTTP_USER_AGENT'), 'MicroMessenger') !== false;
        $isMobile = $this->request->isMobile();
        $this->view->assign("isWechat", $isWechat);
        $this->view->assign("isMobile", $isMobile);

        //发起PC支付(Scan支付)(PC扫码模式)
        if ($this->request->isAjax()) {
            $pay = Pay::wechat($config);
            $orderid = $this->request->post("orderid");
            try {
                $result = $pay->find($orderid, 'scan');
                if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {
                    $this->success("", "", ['status' => $result['trade_state']]);
                } else {
                    $this->error("查询失败");
                }
            } catch (GatewayException $e) {
                $this->error("查询失败");
            }
        }

        $orderData = Session::get("wechatorderdata");
        if (!$orderData) {
            $this->error("请求参数错误");
        }
        if ($isWechat && $isMobile) {
            //发起公众号(jsapi支付),openid必须

            //如果没有openid，则自动去获取openid
            if (!isset($orderData['openid']) || !$orderData['openid']) {
                $orderData['openid'] = Service::getOpenid();
            }

            $orderData['method'] = 'mp';
            $type = 'jsapi';
            $payData = Service::submitOrder($orderData);
            if (!isset($payData['paySign'])) {
                $this->error("创建订单失败，请返回重试", "");
            }
        } else {
            $orderData['method'] = 'scan';
            $type = 'pc';
            $payData = Service::submitOrder($orderData);
            if (!isset($payData['code_url'])) {
                $this->error("创建订单失败，请返回重试", "");
            }
        }
        $this->view->assign("orderData", $orderData);
        $this->view->assign("payData", $payData);
        $this->view->assign("type", $type);

        $this->view->assign("title", "微信支付");
        return $this->view->fetch();
    }

    /**
     * 支付宝支付(PC扫码支付)
     * @return string
     */
    public function alipay()
    {
        $config = Service::getConfig('alipay');

        $isWechat = stripos($this->request->server('HTTP_USER_AGENT'), 'MicroMessenger') !== false;
        $isMobile = $this->request->isMobile();
        $this->view->assign("isWechat", $isWechat);
        $this->view->assign("isMobile", $isMobile);

        if ($this->request->isAjax()) {
            $orderid = $this->request->post("orderid");
            $pay = Pay::alipay($config);
            try {
                $result = $pay->find($orderid, 'scan');
                if ($result['code'] == '10000' && $result['trade_status'] == 'TRADE_SUCCESS') {
                    $this->success("", "", ['status' => $result['trade_status']]);
                } else {
                    $this->error("查询失败");
                }
            } catch (GatewayException $e) {
                $this->error("查询失败");
            }
        }

        //发起PC支付(Scan支付)(PC扫码模式)
        $orderData = Session::get("alipayorderdata");
        if (!$orderData) {
            $this->error("请求参数错误");
        }

        $orderData['method'] = 'scan';
        $payData = Service::submitOrder($orderData);
        if (!isset($payData['qr_code'])) {
            $this->error("创建订单失败，请返回重试");
        }

        $type = 'pc';
        $this->view->assign("orderData", $orderData);
        $this->view->assign("payData", $payData);
        $this->view->assign("type", $type);
        $this->view->assign("title", "支付宝支付");
        return $this->view->fetch();
    }

    /**
     * 支付成功回调
     */
    public function notifyx()
    {   
        $data = $this->request->param();
        $paytype = $this->request->param('type');
        if($paytype=='wechat'){
            $pay = \addons\epay\library\Service::checkNotify($paytype);
            if (!$pay) {
                echo '签名错误';
                return;
            }
            $data = $pay->verify();
            $order_sn = $data['out_trade_no'];
            $out_sn = $data['transaction_id'];
            $money = $data['total_fee']/100;
        }else{
            $order_sn = $this->request->param('order_sn');
            $redis = (new Redis())->handler;
            $redis->select(7);
            $data = $redis->hgetall("order_sn_{$order_sn}");
            if(!$data){echo '验签失败';return '';}
            $order_sn = $data['out_trade_no'];
            $out_sn = $data['trade_no'];
            $money = $data['total_amount'];
        }

        $ot = substr($order_sn,0,2);

        switch($ot){
            case 'RE'://充值钻石
                $this->recharge_order($order_sn,$paytype,$out_sn,$money);
                break;
            case 'VI'://vip
                $this->vip_buy_order($order_sn,$paytype,$out_sn,$money);
                break;
        }
        
        //你可以在这里你的业务处理逻辑,比如处理你的订单状态、给会员加余额等等功能
        //下面这句必须要执行,且在此之前不能有任何输出
        if($paytype=='wechat'){
            return $pay->success()->send();
        }else{
            $redis->del("order_sn_{$order_sn}");
            return "{}";
        }
    }
    
    
    
    /**
     * 支付成功返回
     */
    public function returnx()
    {
        $type = $this->request->param('type');
        if (Service::checkReturn($type)) {
            echo '签名错误';
            return;
        }

        //你可以在这里定义你的提示信息,但切记不可在此编写逻辑
        $this->success("恭喜你！支付成功!", addon_url("epay/index/index"));

        return;
    }
    
    
    
    
    
    public function submit_other_api($order_sn,$pay_type,$amount, $title){
        //订单标题
        // $title = '支付订单';
        //回调链接
        // $notifyurl = $this->request->root(true) . '/addons/epay/api/notifyx/type/' . $pay_type;
        if($pay_type=='alipay'){
            $notifyurl = 'http://ywtcweb.yewan8888.com:9501/order/alipay';
        }elseif($pay_type=='wechat'){
            $notifyurl = 'http://ywtcweb.yewan8888.com/addons/epay/api/notifyx/type/'.$pay_type;
        }
        
        $returnurl = $this->request->root(true) . '/addons/epay/api/returnx/type/' . $pay_type . '/out_trade_no/' . $order_sn;

        $response = Service::submitOrder($amount, $order_sn, $pay_type, $title, $notifyurl, $returnurl, 'app');//dump(json_decode($response->getContent(),true));
        switch($pay_type){
            case 'wechat':
                return json_decode($response->getContent(),true);
                break;
            default:
                return $response->getContent();
                break;
        }
        
    }



    //付款回调
    public function recharge_order($order_sn,$pay_type,$out_sn,$money){//$order_sn,$pay_type,$out_sn
        $info = Db::name('recharge_order')->where('order_sn',$order_sn)->find();
        if($info['status']!='0'){return ;}
        $time = time();
        //订单表
        $save = [];
        $save['paytime'] = $time;
        $save['updatetime'] = $time;
        $save['status'] = '1';
        $save['out_sn'] = $out_sn;
        $save['payamount'] = $money;
        switch($pay_type){
            case 'alipay':
                $save['paytype'] = '1';
                $msg = '充值钻石';
                break;
            case 'wechat':
                $save['paytype'] = '2';
                $msg = '充值钻石';
                break;
            case 'yue':
                $save['paytype'] = '4';
                $msg = '佣金兑换钻石';
                break;
            case 'pg':
                $save['paytype'] = '3';
                $msg = '充值钻石';
        }
        
        //改状态，发钻石，写钻石日志，【写余额日志】
        Db::name('recharge_order')->where('id',$info['id'])->update($save);
        \app\common\model\User::score($info['diamond'], $info['user_id'], $msg);
        
        if($pay_type!='yue'){
            $if = Db::name('user')->where('id',$info['user_id'])->value('first_recharge');
            if($if=='0'){
                Db::name('user')->where('id',$info['user_id'])->update(['first_recharge'=>'1']);
            }
        }
        
        return ;
    }


    //处理VIP 订单
    //自行处理订单表，模型处理顺延时间和头衔
    public function vip_buy_order($order_sn,$type,$out_sn,$money){
        // $req = Request::instance();
        // $order_sn = $req->param('order_sn');
        // $type = $req->param('type');
        // $out_sn = $req->param('out_sn');
        // $money = $req->param('money');
        $info = Db::name('vip_order')->alias('a')
        ->join('user b','b.id=a.user_id','left')
        ->field('a.*,b.p_user,b.gender,b.if_effective,b.is_vip')
        ->where('order_sn',$order_sn)
        ->find();
        if(empty($info)){
            return ;
        }

        if($info['status']!='0'){
            return ;
        }else{
            $save = [
                'status'=>'1',
                'pay_type'=>$type,
                'pay_time'=>time(),
                'out_sn'=>$out_sn
            ];
            $R = Db::name('vip_order')->where('id',$info['id'])->update($save);
            \app\common\model\User::vip_update_user($info['user_id'],$info['month'],$info['unit']);
            if($info['is_vip']!='one'){//初次更新次数
                $redis = (new Redis())->handler;
                $redis->select(2);
                $data = $redis->hmget('global_config',['unlock_chat_num','unlock_weixin_num','unlock_chatup_vip']);
                Db::name('user')->where('id',$info['user_id'])->update(['unlock_chat'=>$data['unlock_chat_num'],'unlock_weixin'=>$data['unlock_weixin_num'],'unlock_chatup'=>$data['unlock_chatup_vip']]);
            }
            if($info['p_user'] && $info['gender']=='1'){
                $rate = Config::get('site.y_parent_rate');
                $p_money = sprintf("%.2f",$money*$rate*0.01);
                if($p_money){
                    Db::name('user')->where('id',$info['p_user'])->inc('son_money',$p_money)->update();
                    \app\common\model\User::money($p_money, $info['p_user'], '下级男用户开通会员，获得分佣');
                }
                
                if($info['if_effective']=='0'){
                    Db::name('user')->where('id',$info['user_id'])->update(['if_effective'=>'1']);
                    Db::name('user')->where('id',$info['p_user'])->inc('son_num',1)->update();
                }
            }
        }
    }

}
