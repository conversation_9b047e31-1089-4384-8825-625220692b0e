<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id}">
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}" readonly="readonly">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Unlock_chat')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unlock_chat" min="0" class="form-control" name="row[unlock_chat]" type="number" value="{$row.unlock_chat|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Unlock_weixin')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unlock_weixin" min="0" class="form-control" name="row[unlock_weixin]" type="number" value="{$row.unlock_weixin|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Unlock_chatup')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unlock_chatup" min="0" class="form-control" name="row[unlock_chatup]" type="number" value="{$row.unlock_chatup|htmlentities}">
        </div>
    </div>
    
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Txy_times')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-txy_times" class="form-control" name="row[txy_times]" type="number" value="{$row.txy_times|htmlentities}">
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
