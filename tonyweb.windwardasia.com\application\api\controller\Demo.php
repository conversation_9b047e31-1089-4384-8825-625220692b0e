<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Redis;
use app\pay\common\Lxpay;
use http\Env;

/**
 * 示例接口
 */
class Demo extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];

    /**
     * 测试方法
     *
     * @ApiTitle    (测试名称)
     * @ApiSummary  (测试描述信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/api/demo/test/id/{id}/name/{name})
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="id", type="integer", required=true, description="会员ID")
     * @ApiParams   (name="name", type="string", required=true, description="用户名")
     * @ApiParams   (name="data", type="object", sample="{'user_id':'int','user_name':'string','profile':{'email':'string','age':'integer'}}", description="扩展数据")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="0")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="返回成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'user_id':'int','user_name':'string','profile':{'email':'string','age':'integer'}}", description="扩展数据返回")
     * @ApiReturn   ({
    'code':'1',
    'msg':'返回成功'
    })
     */
    public function test()
    {
        $this->success('返回成功', $this->request->param());
    }

    /**
     * 无需登录的接口
     *
     */
    public function test1()
    {
        $this->success('返回成功', ['action' => 'test1']);
    }

    /**
     * 需要登录的接口
     *
     */
    public function test2()
    {
        $this->success('返回成功', ['action' => 'test2']);
    }

    /**
     * 需要登录且需要验证有相应组的权限
     *
     */
    public function test3()
    {
        $this->success('返回成功', ['action' => 'test3']);
    }

    public function index()
    {
    }

    public $a = [];

    public function demo_pay()
    {
        $order_sn   = 'VI202410101025540001707685429';
        $money      = 100;
        $notify_url = \think\Env::get('url.uri') . 'api/demo/notify';
        $desc       = '购买vip';
        $url        = \think\Env::get('url.uri') . 'api/index/ls_pay?';
        $post_data  = http_build_query(compact('order_sn', 'money', 'notify_url', 'desc'));
        $result     = file_get_contents($url . $post_data);
        $result     = json_decode($result, true);
        var_dump($result);
        die;
    }


    /**
     * 回调处理
     * @return false|void
     */
    public function notify()
    {
        $param = $this->request->param();
        $pay   = new Lxpay();
        $data  = $pay->decrypt($param);
        if (!$data) return false;
        $out_order_no = $data['Result']['MerchantOrderNo'];
        $amount       = $data['Result']['Amt'];
    }


}
