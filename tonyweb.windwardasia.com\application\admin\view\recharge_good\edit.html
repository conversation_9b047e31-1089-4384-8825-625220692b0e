<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <!--<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lable')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lable" class="form-control" name="row[lable]" type="text" value="{$row.lable|htmlentities}">
        </div>
    </div>
   <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Good_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-good_code" class="form-control" name="row[good_code]" type="text" value="{$row.good_code|htmlentities}">
        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-price" class="form-control" step="0.01" name="row[price]" type="number" value="{$row.price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Diamond')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-diamond" min="0" class="form-control" name="row[diamond]" type="number" value="{$row.diamond|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zengsong_diamond')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zengsong_diamond" min="0" class="form-control" name="row[zengsong_diamond]" type="number" value="{$row.zengsong_diamond|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('On_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-on_switch" min="0" class="form-control selectpicker" name="row[on_switch]">
                {foreach name="onSwitchList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.on_switch"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" min="0" class="form-control" name="row[weigh]" type="number" value="{$row.weigh|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
