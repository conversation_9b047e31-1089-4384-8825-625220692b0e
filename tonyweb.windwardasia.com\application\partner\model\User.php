<?php

namespace app\partner\model;

use app\common\model\MoneyLog;
use app\common\model\ScoreLog;
use think\Model;

class User extends Model
{

    // 表名
    protected $name = 'user';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
        'prevtime_text',
        'logintime_text',
        'jointime_text',
        'gender_text',
        'is_vip_text',
        'vip_end_time_text',
        'is_auth_text',
        'first_recharge_text',
        'brand_new_text',
        'girl_switch_text',
        'top_switch_text',
        'nearby_hide_text',
        'distance_hide_text',
        'exit_time_text',
        'have_exit_text',
        'img_auth_text',
        'video_auth_text',
        'forever_on_text',
        'need_edit_text'
    ];

    public function getOriginData()
    {
        return $this->origin;
    }

    protected static function init()
    {
        self::beforeUpdate(function ($row) {
            $changed = $row->getChangedData();
            //如果有修改密码
            if (isset($changed['password'])) {
                if ($changed['password']) {
                    $salt = \fast\Random::alnum();
                    $row->password = \app\common\library\Auth::instance()->getEncryptPassword($changed['password'], $salt);
                    $row->salt = $salt;
                } else {
                    unset($row->password);
                }
            }
        });


        self::beforeUpdate(function ($row) {
            $changedata = $row->getChangedData();
            $origin = $row->getOriginData();
            if (isset($changedata['money']) && (function_exists('bccomp') ? bccomp($changedata['money'], $origin['money'], 2) !== 0 : (double)$changedata['money'] !== (double)$origin['money'])) {
                MoneyLog::create(['user_id' => $row['id'], 'money' => $changedata['money'] - $origin['money'], 'before' => $origin['money'], 'after' => $changedata['money'], 'memo' => '管理员变更金额']);
            }
            if (isset($changedata['score']) && (int)$changedata['score'] !== (int)$origin['score']) {
                ScoreLog::create(['user_id' => $row['id'], 'score' => $changedata['score'] - $origin['score'], 'before' => $origin['score'], 'after' => $changedata['score'], 'memo' => '管理员变更天使币']);
            }
        });
    }

    
    public function getApplyPartnerStatusList(){
        return ['0' => __('partner_status 0'), '1' => __('partner_status 1')];
    }

    public function getPartnerStatusList(){
        return ['0' => __('partner_status 0'), '1' => __('partner_status 1'),'2' => __('partner_status 2'), '3' => __('partner_status 3')];
    }

    public function getIsPartnerList(){
        return ['1' => __('is_partner 1'), '0' => __('is_partner 0')];
    }
    
    public function getStatusList(){
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    public function getPrevtimeTextAttr($value, $data)
    {
        $value = $value ? $value : ($data['prevtime'] ?? "");
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getLogintimeTextAttr($value, $data)
    {
        $value = $value ? $value : ($data['logintime'] ?? "");
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getJointimeTextAttr($value, $data)
    {
        $value = $value ? $value : ($data['jointime'] ?? "");
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setPrevtimeAttr($value)
    {
        return $value && !is_numeric($value) ? strtotime($value) : $value;
    }

    protected function setLogintimeAttr($value)
    {
        return $value && !is_numeric($value) ? strtotime($value) : $value;
    }

    protected function setJointimeAttr($value)
    {
        return $value && !is_numeric($value) ? strtotime($value) : $value;
    }

    protected function setBirthdayAttr($value)
    {
        return $value ? $value : null;
    }

    public function group(){
        return $this->belongsTo('UserGroup', 'group_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    // public function partner(){
    //     return $this->belongsTo('PartnerLevel', 'partner_id', 'id', [], 'LEFT')->setEagerlyType(0);
    // }
    
    public function getGenderList()
    {
        return ['1' => __('Gender 1'), '2' => __('Gender 2'), '0' => __('Gender 0')];
    }

    public function getIsVipList()
    {
        return ['one' => __('Is_vip one'), 'three' => __('Is_vip three')];
    }
    
    //编辑资料:1=已编辑,0=未编辑
    public function getNeedEditList(){
        return ['1' => __('Need_edit 1'), '0' => __('Need_edit 0')];
    }
    
    protected function getNeedEditTextAttr($value, $data){
        $list = $this->getNeedEditList();
        return isset($data['need_edit'])?$list[$data['need_edit']]:'';
    }

    public function getIsAuthList()
    {
        return ['0' => __('Is_auth 0'), '1' => __('Is_auth 1')];
    }

    public function getFirstRechargeList()
    {
        return ['0' => __('First_recharge 0'), '1' => __('First_recharge 1')];
    }

    public function getBrandNewList()
    {
        return ['0' => __('Brand_new 0'), '1' => __('Brand_new 1')];
    }

    public function getGirlSwitchList()
    {
        return ['1' => __('Girl_switch 1'), '0' => __('Girl_switch 0')];
    }

    public function getTopSwitchList()
    {
        return ['0' => __('Top_switch 0'), '1' => __('Top_switch 1')];
    }

    public function getNearbyHideList()
    {
        return ['1' => __('Nearby_hide 1'), '0' => __('Nearby_hide 0')];
    }

    public function getDistanceHideList()
    {
        return ['1' => __('Distance_hide 1'), '0' => __('Distance_hide 0')];
    }

    public function getHaveExitList()
    {
        return ['0' => __('Have_exit 0'), '1' => __('Have_exit 1')];
    }

    public function getImgAuthList()
    {
        return ['0' => __('Img_auth 0'), '1' => __('Img_auth 1'),'3' => __('Img_auth 3')];
    }

    public function getVideoAuthList()
    {
        return ['0' => __('Video_auth 0'), '1' => __('Video_auth 1'), '2' => __('Video_auth 2'), '3' => __('Video_auth 3')];
    }

    public function getForeverOnList()
    {
        return ['1' => __('Forever_on 1'), '0' => __('Forever_on 0')];
    }


    public function getGenderTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['gender']) ? $data['gender'] : '');
        $list = $this->getGenderList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getIsVipTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_vip']) ? $data['is_vip'] : '');
        $list = $this->getIsVipList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getVipEndTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['vip_end_time']) ? $data['vip_end_time'] : '');
        return is_numeric($value)&&$value>0 ? date("Y-m-d H:i:s", $value) : '无';
    }


    public function getIsAuthTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_auth']) ? $data['is_auth'] : '');
        $list = $this->getIsAuthList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getFirstRechargeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['first_recharge']) ? $data['first_recharge'] : '');
        $list = $this->getFirstRechargeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getBrandNewTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['brand_new']) ? $data['brand_new'] : '');
        $list = $this->getBrandNewList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getGirlSwitchTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['girl_switch']) ? $data['girl_switch'] : '');
        $list = $this->getGirlSwitchList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getTopSwitchTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['top_switch']) ? $data['top_switch'] : '');
        $list = $this->getTopSwitchList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getNearbyHideTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['nearby_hide']) ? $data['nearby_hide'] : '');
        $list = $this->getNearbyHideList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getDistanceHideTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['distance_hide']) ? $data['distance_hide'] : '');
        $list = $this->getDistanceHideList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getExitTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['exit_time']) ? $data['exit_time'] : '');
        return (is_numeric($value) && $value) ? date("Y-m-d H:i:s", $value) : '无';
    }


    public function getHaveExitTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['have_exit']) ? $data['have_exit'] : '');
        $list = $this->getHaveExitList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getImgAuthTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['img_auth']) ? $data['img_auth'] : '');
        $list = $this->getImgAuthList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getVideoAuthTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['video_auth']) ? $data['video_auth'] : '');
        $list = $this->getVideoAuthList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getForeverOnTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['forever_on']) ? $data['forever_on'] : '');
        $list = $this->getForeverOnList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setVipEndTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setExitTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }
    
}
