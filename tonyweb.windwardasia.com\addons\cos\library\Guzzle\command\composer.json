{"name": "guzzlehttp/command", "description": "Provides the foundation for building command-based web service clients", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "require": {"php": "^7.2.5 || ^8.0", "guzzlehttp/guzzle": "^7.4.1", "guzzlehttp/promises": "^1.5.1", "guzzlehttp/psr7": "^1.8.3 || ^2.1"}, "require-dev": {"phpunit/phpunit": "^8.5.19"}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "config": {"preferred-install": "dist", "sort-packages": true}}