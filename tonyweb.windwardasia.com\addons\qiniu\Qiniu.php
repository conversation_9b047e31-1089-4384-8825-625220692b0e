<?php

namespace addons\qiniu;

use fast\Http;
use <PERSON>iu\Auth;
use think\Addons;
use think\App;
use think\Loader;

/**
 * 七牛云储存插件
 */
class <PERSON><PERSON> extends Addons
{

    /**
     * 插件安装方法
     * @return bool
     */
    public function install()
    {
        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall()
    {
        return true;
    }

    /**
     * 判断是否来源于API上传
     */
    public function moduleInit($request)
    {
        $config = $this->getConfig();
        $module = strtolower($request->module());
        if ($module == 'api' && ($config['apiupload'] ?? 0) && in_array($module, explode(',', $config['uploadmodule'] ?? '')) &&
            strtolower($request->controller()) == 'common' &&
            strtolower($request->action()) == 'upload') {
            request()->param('isApi', true);
            App::invokeMethod(["\\addons\\qiniu\\controller\\Index", "upload"], ['isApi' => true]);
        }
    }

    /**
     * 上传初始化时
     */
    public function uploadConfigInit(&$upload)
    {
        $config = $this->getConfig();
        $module = request()->module();
        $module = $module ? strtolower($module) : 'index';

        $uploadModule = array_filter(explode(',', $config['uploadmodule'] ?? ''));
        if (!in_array($module, $uploadModule)) {
            return $upload;
        }

        $policy = array(
            'saveKey' => ltrim($config['savekey'], '/'),
        );

        $config['savekey'] = str_replace(['$(year)', '$(mon)', '$(day)', '$(etag)', '$(ext)'], ['{year}', '{mon}', '{day}', '{filemd5}', '{.suffix}'], $config['savekey']);
        $auth = new Auth($config['accessKey'], $config['secretKey']);

        $token = '';
        $noNeedLogin = array_filter(explode(',', $config['noneedlogin'] ?? ''));
        if (in_array($module, $noNeedLogin) || ($module == 'admin' && \app\admin\library\Auth::instance()->id) || ($module != 'admin' && \app\common\library\Auth::instance()->id)) {
            $token = $auth->uploadToken($config['bucket'], null, $config['expire'], $policy);
        }
        $multipart = [
            'qiniutoken' => $token
        ];

        $upload = array_merge($upload, [
            'cdnurl'     => $config['cdnurl'],
            'uploadurl'  => $config['uploadmode'] == 'client' ? $config['uploadurl'] : addon_url('qiniu/index/upload', [], false, true),
            'uploadmode' => $config['uploadmode'],
            'bucket'     => $config['bucket'],
            'maxsize'    => $config['maxsize'],
            'mimetype'   => $config['mimetype'],
            'savekey'    => $config['savekey'],
            'chunking'   => (bool)($config['chunking'] ?? $upload['chunking']),
            'chunksize'  => (int)($config['chunksize'] ?? $upload['chunksize']),
            'multipart'  => $multipart,
            'storage'    => $this->getName(),
            'multiple'   => $config['multiple'] ? true : false,
        ]);
    }

    /**
     * 附件删除后
     */
    public function uploadDelete($attachment)
    {
        $config = $this->getConfig();
        if ($attachment['storage'] == 'qiniu' && isset($config['syncdelete']) && $config['syncdelete']) {
            $auth = new Auth($config['accessKey'], $config['secretKey']);
            $entry = $config['bucket'] . ':' . ltrim($attachment->url, '/');
            $encodedEntryURI = \Qiniu\base64_urlSafeEncode($entry);
            $url = 'http://rs.qiniu.com/delete/' . $encodedEntryURI;
            $headers = $auth->authorization($url);
            //删除云储存文件
            $ret = Http::sendRequest($url, [], 'POST', [CURLOPT_HTTPHEADER => ['Authorization: ' . $headers['Authorization']]]);
            //如果是服务端中转，还需要删除本地文件
            //if ($config['uploadmode'] == 'server') {
            //    $filePath = ROOT_PATH . 'public' . str_replace('/', DS, $attachment->url);
            //    if ($filePath) {
            //        @unlink($filePath);
            //    }
            //}
        }
        return true;
    }

    public function appInit()
    {
        if (!class_exists('\Qiniu\Config')) {
            Loader::addNamespace('Qiniu', ADDON_PATH . 'qiniu' . DS . 'library' . DS . 'Qiniu' . DS);
            require_once ADDON_PATH . 'qiniu' . DS . 'library' . DS . 'Qiniu' . DS . 'functions.php';
        }
    }

}
