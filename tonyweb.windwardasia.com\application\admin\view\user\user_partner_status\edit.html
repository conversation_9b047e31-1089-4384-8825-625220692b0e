<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Partner_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-partner_id" data-rule="required" data-source="partner/index" class="form-control selectpage" name="row[partner_id]" type="text" value="{$row.partner_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Old_is_partner')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-old_is_partner" class="form-control" name="row[old_is_partner]" type="text" value="{$row.old_is_partner|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('New_is_partner')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-new_is_partner" class="form-control" name="row[new_is_partner]" type="text" value="{$row.new_is_partner|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Old_partner_level')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-old_partner_level" class="form-control" name="row[old_partner_level]" type="text" value="{$row.old_partner_level|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('New_partner_level')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-new_partner_level" class="form-control" name="row[new_partner_level]" type="text" value="{$row.new_partner_level|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
